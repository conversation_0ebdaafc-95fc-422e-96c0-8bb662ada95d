# 商用密码培训课程——密码应用改造工作量评估

==Start of OCR for page 1==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

# 商用密码培训课程

## ——密码应用改造工作量评估

**商密产品线**
www.dbappsecurity.com.cn 400-6059-110

*(Image: A modern office building, 安恒大厦)*
==End of OCR for page 1==

==Start of OCR for page 2==
**Header:** 安恒信息 | 构建安全可信的数字世界 (DAS-Security | Build A Secure And Credible Digital World)

# 目录 (Contents)

* **01** 为什么要知道密码应用改造评估?
* **02** 密码应用改造到底需要改什么?
* **03** 密码应用改造工作量一览
  ==End of OCR for page 2==

==Start of OCR for page 3==

# 01 | 为什么要知道密码应用改造评估?

==End of OCR for page 3==

==Start of OCR for page 4==
**Header:** 商密合规是业务, 不仅仅是卖产品 | 安恒信息 | 构建安全可信的数字世界

**商密合规业务 = 规划方案 + 密码产品 + 集成改造 + 应用测评**
如果说合规业务是一场战斗, **集成改造**是一个必然面对也必须拿下的高地。

### 密评项目参与方与协作流程

* **密码厂商:**
  * **职责:** 1. 密码应用方案、实施方案等编制; 2. 协助开展系统密码改造工作; 3. 配合密码测评工作; 4. 产品安装实施。
  * **交互:** -> (提供方案、清单、预算) -> **业主单位**; -> (系统合规性开发改造) -> **系统集成商**。
* **设计单位 (外部专家):**
  * **职责:** 1. 方案调研; 2. 可行性研究报告; 3. 方案设计; 4. 第三方专家评审。
  * **交互:** <- (委托设计) - **业主单位**。
* **业主单位 (系统开发商):**
  * **核心方**，驱动整个项目。
  * **交互:** -> (委托测评) -> **密评机构**。
* **系统集成商:**
  * **职责:** 系统合规性开发改造。
* **密评机构:**
  * **职责:** 方案测评与实施测评, 提供评估报告。
  * **交互:** -> (方案及实施测评报告同步，盖章备案) -> **密码管理局**。
* **密码管理局 (监管单位):**
  * **职责:** 1. 对测评机构进行监督管理; 2. 对测评系统进行抽查。

**最终目标:** 密码应用项目成功。
==End of OCR for page 4==

==Start of OCR for page 5==
**Header:** 为什么要知道密码改造评估? | 安恒信息 | 构建安全可信的数字世界

**核心思想：事先勘查地形是拿下高地的前提。**

### 我们的能力边界

* **能力圈 (能掌控):**
  * 密码产品
  * 密码方案
* **影响区 (很重要，不一定能掌控):**
  * 业主支持
  * 测评机构支持
* **知道存在 (很重要，无法掌控):**
  * 友商竞争
  * **集成改造** (由业务系统开发商执行)
* **知道存在 (不关键，无法掌控)**

**解读:**

* **能力圈:** 我们能直接掌握的是密码产品和密码应用方案。
* **影响区:** 通过商务关系能影响的是业主单位、测评机构的支持。
* **知道存在:** 很重要但不一定能掌控的是友商的竞争, 以及业务系统开发(供应)商的合作态度。
* **关键点:** 密评项目往往对开发商而言ROI比较低, 配合基本只能靠业主施压。但是这里面开发工作如何执行的话语权在开发商, 如果我们对其开发内容不能有一个基本认知, 往往会陷入比较被动的局面。**如果我们能够对开发商的工作内容有一个基本认识, 并能给业主方一个参考, 有机会化被动为主动。**
  ==End of OCR for page 5==

==Start of OCR for page 6==

# 02 | 密码应用改造到底需要改什么?

==End of OCR for page 6==

==Start of OCR for page 7==
**Header:** 方案视角-密码应用改造 | 安恒信息 | 构建安全可信的数字世界

*(Image: A detailed topology diagram for a Level-3 cryptographic application security system.)*

如上图所示, 在**GB/T39786第三级**密码应用标准下, 常见的密码设备部署拓扑。密码属于信息系统底层的基础资源, 因此很少有开箱即用的密码产品。合规且正确的密码产品应用, 或多或少都会涉及原业务系统的开发改造工作。本文旨在将这部分改造的工作内容进行重难点识别, 并给出一定的改造工作量化参考。

对照39786并结合实际项目经验, **改造工作主要涉及以下内容:**

* 业务系统改造适配**安全认证网关/签名验签服务器**, 能够**基于数字证书(USBkey)进行身份鉴别**。
* 业务系统集成**云密盾SDK**并修改数据传输机制, 实现**数据传输的机密性和完整性保护**。
* 业务系统调用**服务器密码机**并修改存储读写机制, 实现**数据存储的机密性和完整性保护**。
* 网络边界安全设备需要做改造, 调用服务器密码机。
* 通用服务器/网络设备等调用服务器密码机实现**访问控制信息的完整性保护**。
* 通用服务器/网络设备等调用服务器密码机实现**日志记录的完整性保护**。
  ==End of OCR for page 7==

==Start of OCR for page 8==

### 看标准, 身份鉴别实现的两种模式

#### 安全认证网关应用场景 (代理模式)

* **标准定义:** GB/T 15843.3 图2 - 代理模式下双向身份鉴别机制。
* **流程描述:**
  1. **用户A** 向 **代理身份鉴别服务T (安全认证网关)** 发送证书 `CertA` 和认证令牌 `TokenAT`。
  2. **代理T** 验证用户A的身份。
  3. **代理T** 向 **用户A** 发送自己的证书 `CertT` 和认证令牌 `TokenTA`。
  4. **用户A** 验证代理T的身份。
  5. 验证通过后，**代理T** 将用户A的身份信息传递给后端的 **应用B**。

#### 签名验签服务器应用场景 (调用模式)

* **标准定义:** GB/T 15843.3 图3 - 调用模式下的身份鉴别机制。
* **流程描述:**
  1. **应用B** 向 **用户A** 发送随机数 `RB` 和可选文本 `Text` 作为挑战。
  2. **用户A** 向 **应用B** 发送自己的证书 `CertA` 和包含对挑战信息签名的认证令牌 `TokenAB`。
  3. **应用B** 通过调用 **身份鉴别服务T (签名验签服务器)** 来对用户A的身份进行验证。
     ==End of OCR for page 8==

==Start of OCR for page 9==

### 应用和数据安全层面改造 - 身份鉴别 (安全认证网关)

#### 部署安全认证网关前后登录流程对比

| 传统B/S登录流程                   | 采用安全认证网关使用证书的登录流程                                               |
| :-------------------------------- | :------------------------------------------------------------------------------- |
| 1. 用户通过HTTP向服务端发送请求。 | 1. 用户通过HTTPS向安全认证网关发送请求。                                         |
| 2. 服务端返回登陆页面。           | 2. 安全认证网关要求用户提交证书。                                                |
| 3. 用户输入用户名和口令。         | 3. 用户输入证书设备口令(PIN码)提交证书。                                         |
| 4. 服务从请求中获取用户名和口令。 | 4. (后台)安全认证网关与浏览器自动交互，验证用户证书。                            |
| 5. 校验用户名口令是否匹配。       | 5. (后台)网关将请求与用户证书信息发送到服务器。                                  |
| 6. 匹配成功则登录。               | 6.**(改造点)** 服务从请求中获取用户证书信息(如 `request.getCookies()`)。 |
|                                   | 7.**(改造点)** 校验用户证书信息是否存在。                                  |
|                                   | 8. 存在则登录成功。                                                              |

#### 业务系统改造工作界面

**通过安全认证网关进行身份鉴别的基本流程:**

1. 用户通过浏览器访问系统地址, 插入Ukey后在弹出认证页面上输入PIN码, 通过浏览器提交数字证书。
2. 安全认证网关与浏览器交互完成对数字证书的认证。
3. 网关把通过认证的用户证书信息发送给应用系统(证书信息嵌入到HTTP协议中), 系统获取相应的cookie信息就可以获取网关校验后的证书信息, 并与系统数据库中的证书信息、用户信息进行匹配。
4. 匹配成功则通过验证。

**业务系统需改造实现 (约5人天):**

* **接收**安全认证网关传递的证书信息。
* 原用户名/口令体系的身份鉴别方式需**增加和数字证书的关联**。
  ==End of OCR for page 9==

==Start of OCR for page 10==

### 应用和数据安全层面改造 - 身份鉴别 (签名验签服务器)

#### 调用签名验签服务器进行身份鉴别的登录流程

1. **用户**从CA机构获取证书。
2. 用户发起登录请求，输入**用户名+口令**。
3. **业务系统**先校验用户名口令，再向用户发起**证书认证挑战**（生成随机数）。
4. 用户通过**USBKey+PIN码**，提供**数字证书**和对随机数的**签名信息**。
5. **业务系统**获取数字证书和签名信息，并调用**签名验签服务器**验证证书信息。
6. 验证成功则登录通过，否则登录失败。

**业务系统需改造实现 (约7 ~ 15人天):**

* 集成**签名验签SDK模块**。
* 原用户名/口令体系的身份鉴别方式需**增加和数字证书的关联**。
* **鉴权机制需改变**。
  ==End of OCR for page 10==

==Start of OCR for page 11==

### 身份鉴别改造 - 智能密码钥匙

**智能密码钥匙 (USBKey)** 是一种实现密码运算、密钥管理功能，提供密码服务的终端密码设备。

* **功能:**
  * **密码运算:** 支持SM4、SM2、SM3。
  * **密钥管理:** 包括生成随机数、密钥的生成、存储、使用、导入、导出、协商等。

**智能密码钥匙潜在的改造需求说明:**

* 用户数字证书是密码应用项目中不可或缺的一环。但用户侧CA数字证书的采购往往有很强的**地域/行业隔阂**，各大CA机构数字证书采用的USBkey也往往来自不同的上游供应商。
* 密码设备 (SSL VPN、安全认证网关、签名验签服务器等) 对来自不同供应商的USBkey需要做**一定的驱动适配**。

**工作量估算:**

* 项目中需要进一步识别该CA使用的USBkey品牌，海泰方圆已经实现主要驱动的对接, 具体型号适配需要约**3人天**的工作量。
* 其他品牌USBkey适配工作量约在**7人天**。
  ==End of OCR for page 11==

==Start of OCR for page 12==

### 数据传输的机密性和完整性

#### 业务系统集成云密盾SDK实现传输机密性和完整性保护的流程

* **流程概述:** 客户端与服务端通过**协同签名**进行身份认证，协商出**加密密钥**后，使用该密钥通过**SDK密码模块**对传输的报文进行加解密和完整性保护。

**业务系统需改造实现 (约3 ~ 7人天):**

* 数据传输机密性完整性进行改造在密评合规方案中性价比不高, **不主推**。
* **集成云密盾SDK模块**。
* **数据传输机制修改**。
  ==End of OCR for page 12==

==Start of OCR for page 13==

### 数据存储的机密性和完整性

#### 业务系统调用服务器密码机实现数据存储机密性和完整性保护的流程

* **写数据过程:** 应用在写入数据库前，判断数据是否为敏感数据，如果是，则调用密码机SDK，将明文发送给服务器密码机，密码机返回密文和HMAC值，应用将密文和HMAC值存入数据库。
* **读数据过程:** 应用从数据库读出密文和HMAC值，调用密码机SDK，将密文发送给服务器密码机，密码机解密后返回明文和新计算的HMAC'，SDK比对HMAC值一致后，将明文返回给应用。

**业务系统需改造实现 (约3 ~ 7人天):**

* 集成**密码机sdk模块**。
* 数据存储**读写机制修改**。
* **SM3/SM4Utils程序适配**。

**此外，若需实现访问控制信息完整性保护 (约7 ~ 15人天):**

* 集成密码机sdk模块。
* 访问控制信息读写机制修改。
* SM3Utils程序适配。
  ==End of OCR for page 13==

==Start of OCR for page 14==

### 数据存储的机密性和完整性 - 代码参考

*(注：此处为代码示例，将使用代码块格式化)*

#### 完整的SM4实现代码参考 (Go语言示例)

```go
package main

import (
	"bytes"
	"crypto/cipher"
	"encoding/hex"
	"fmt"
	"github.com/tjfoc/gmsm/sm4"
)

// SM4 Encrypt
func SM4Encrypt(data string) (result string, err error) {
    plainText := []byte(data)
    SM4Key := "Uv6tkf2M3xYSRuFv" // Should be read from config
    SM4Iv := "04TzMuvkHm_EZnHm"  // IV should be random
    iv := []byte(SM4Iv)
    key := []byte(SM4Key)

    block, err := sm4.NewCipher(key)
    if err != nil {
        panic(err)
    }

    paddingData := paddingLastGroup(plainText, block.BlockSize())
    blockMode := cipher.NewCBCEncrypter(block, iv)
    cipherText := make([]byte, len(paddingData))
    blockMode.CryptBlocks(cipherText, paddingData)
  
    cipherString := hex.EncodeToString(cipherText)
    return cipherString, nil
}
```

#### 完整的SM3实现代码参考 (Java实例)

```java
package GMSM;

import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.util.encoders.Hex;
import java.util.Arrays;

public class SM3Util {
    // Calculate SM3 hash
    public static byte[] hash(byte[] srcData) {
        SM3Digest digest = new SM3Digest();
        digest.update(srcData, 0, srcData.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }

    // Verify SM3 hash
    public static boolean verify(byte[] srcData, byte[] sm3Hash) {
        byte[] newHash = hash(srcData);
        if (Arrays.equals(newHash, sm3Hash)) {
            return true;
        } else {
            return false;
        }
    }
}
```

**结论:** SM4/SM3Utils在Java/Go/C++等主流的后端开发语言上都有成熟的类/库, 开发人员根据自己业务进行适配即可。当然, 涉及具体业务, 改动的复杂度会有不同, 但区别不大(主要和开发人员的综合素质有关)。
==End of OCR for page 14==

==Start of OCR for page 15==

### 日志记录完整性

#### 日志审计需改造实现 (约7 ~ 15人天)

* **存日志过程:**

  1. 各类设备传输日志到交换机。
  2. 日志被搜集。
  3. 调用**服务器密码机**。
  4. 日志文件到加密机进行加密和HMAC计算。
  5. 加密机返回日志密文和HMAC值。
  6. 保存日志密文和HMAC值到**日志审计(国密改造)**平台。
* **读日志过程:**

  1. **态势感知系统**发起读日志请求。
  2. **日志审计**平台调用服务器密码机，日志密文发至密码机，HMAC保存待比对。
  3. 日志密文到密码机解密并重新做HMAC计算。
  4. 返回解密后的日志和新的HMAC。
  5. SDK比对前后两次的HMAC是否一致，校验通过后返回日志。
  6. 成功获取日志。

**改造内容:**

* 集成**密码机sdk模块**。
* 日志存储**读写机制修改**。
* **SM3/SM4Utils程序适配**。
  ==End of OCR for page 15==

==Start of OCR for page 16==

### 其它可能存在的改造情况

| 测评单元                                   | 涉及的密码产品 | 改造措施                                                           | 预估开发工作量 |
| :----------------------------------------- | :------------- | :----------------------------------------------------------------- | :------------- |
| **网络边界访问控制信息的完整性**     | 服务器密码机   | 由防火墙等边界安全设备厂家调用服务器密码机对其安全设备进行开发改造 | 7 ~ 15人天     |
| **系统资源访问控制信息完整性**       | 服务器密码机   | 通用服务器/数据库等改造后调用服务器密码机                          | 7 ~ 15人天     |
| **重要可执行程序完整性、来源真实性** | 服务器密码机   | 通用服务器/数据库等改造后调用服务器密码机                          | 7 ~ 15人天     |

**改造流程示例 (网络边界访问控制完整性):**

1. 防火墙等设备发起请求，并传输访问控制信息、重要可执行程序。
2. **日志审计(集成SDK)** 调用密码机SDK，同步访问控制信息、重要可执行程序。
3. 数据进行HMAC计算/签名。
4. 返回HMAC/签名。
5. SDK比对前后两次的HMAC是否一致，校验通过可使用访问控制信息、重要可执行程序。
   ==End of OCR for page 16==

==Start of OCR for page 17==

# 03 | 密码应用改造工作量一览

==End of OCR for page 17==

==Start of OCR for page 18==

### 改造工作量明细一览表

*(注：物理和环境层面通过国密门禁/视频系统替换即可实现改造, 这里不再展开)*

| 层面                       | 测评单元                     | 涉及的密码产品                             | 改造措施 (设备厂家工作界面)                                                         | 业务系统厂家工作界面                                                           | 预估开发工作量 |
| :------------------------- | :--------------------------- | :----------------------------------------- | :---------------------------------------------------------------------------------- | :----------------------------------------------------------------------------- | :------------- |
| **网络和通信安全**   | 身份鉴别                     | 合规CA机构SSL站点证书/安全认证网关/SSL VPN | 向被测评系统签发SSL站点证书, 并将证书部署于网关类密码设备上, 由网关对系统进行代理。 | /                                                                              | 不涉及开发     |
|                            | 通信数据完整性               | 安全认证网关/SSL VPN                       | 由密码网关类设备对系统进行SSL代理或搭建国密VPN隧道                                  | /                                                                              | 不涉及开发     |
|                            | 通信过程中重要数据的机密性   | 安全认证网关/SSL VPN                       | 由密码网关类设备对系统进行SSL代理或搭建国密VPN隧道                                  | /                                                                              | 不涉及开发     |
|                            | 网络边界访问控制信息的完整性 | 服务器密码机                               | 由防火墙等边界安全设备厂家调用服务器密码机对其安全设备进行开发改造                  | /                                                                              | 7 ~ 15人天     |
|                            | 身份鉴别                     | SSL VPN/国密堡垒机/USBkey                  | 替换国密堡垒机, 并给运维管理员配发USBkey(数字证书)                                  | /                                                                              | 不涉及开发     |
|                            | 远程管理通道安全             | SSL VPN                                    | 搭建国密VPN隧道                                                                     | /                                                                              | 不涉及开发     |
| **设备和计算安全**   | 系统资源访问控制信息完整性   | 服务器密码机                               | /                                                                                   | 通用服务器/数据库等改造后调用服务器密码机                                      | 7 ~ 15人天     |
|                            | 日志记录完整性               | 服务器密码机                               | /                                                                                   | 通用服务器/数据库等改造后调用服务器密码机                                      | 7 ~ 15人天     |
|                            | 重要可执行程序完整性...      | 服务器密码机                               | /                                                                                   | 通用服务器/数据库等改造后调用服务器密码机                                      | 7 ~ 15人天     |
| **应用和数据安全**   | 身份鉴别                     | 安全认证网关/签名验签服务器/USBkey         | /                                                                                   | 业务系统改造对接安全认证网关、签名验签服务器, 并给相关用户配发USBkey(数字证书) | 5 ~ 15人天     |
|                            | 访问控制信息完整性           | 服务器密码机/签名验签服务器                | /                                                                                   | 业务系统改造后调用服务器密码机                                                 | 7 ~ 15人天     |
|                            | 重要数据传输机密性           | 云密盾                                     | /                                                                                   | 业务系统客户端与服务端改造集成云密盾SDK                                        | 3 ~ 7人天      |
|                            | 重要数据存储机密性           | 服务器密码机                               | /                                                                                   | 业务系统改造后调用服务器密码机                                                 | 3 ~ 14人天     |
|                            | 重要数据传输完整性           | 透传/云密盾                                | /                                                                                   | 业务系统客户端与服务端改造集成云密盾SDK                                        | 3 ~ 7人天      |
|                            | 重要数据存储完整性           | 服务器密码机                               | /                                                                                   | 业务系统改造后调用服务器密码机                                                 | 3 ~ 14人天     |
|                            | 不可否认性                   | 电子签章/时间戳                            | /                                                                                   | 通过电子签章、时间戳服务器等                                                   | /              |
| ==End of OCR for page 18== |                              |                                            |                                                                                     |                                                                                |                |

==Start of OCR for page 19==

### 密码应用改造工具包

商密产品线根据密评/密码应用改造工作存在的共性, 提炼出提升改造效率的工具包。

* **面向测评机构的“密评快速通”**

  * **内容:**
    * 测试改造产品清单
    * 测试方法和工具
    * 三级密评要求逐项测试验证
    * 各类测试用例和实例
  * **目的:** 快速对密码改造后的系统进行验证和评估。
* **面向开发商的开发指导手册**

  * **内容:**
    * **SDK:** 提供 `jar` 包等。
    * **配置文件:** `cacipher.ini` 等。
    * **接口文档:** `服务器密码机JCE用户手册V1.23.pdf` 等。
    * **测试程序:** `JceTestMain20220407.java` 等。
  * **目的:** 为开发人员提供清晰的集成指导和代码示例，降低集成难度，提高开发效率。
    ==End of OCR for page 19==

==Start of OCR for page 20==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

## 联系我们 (Contact Us)

浙江省杭州市滨江区西兴街道联慧街188号安恒大厦
www.dbappsecurity.com.cn
400-6059-110

*(Image: QR Code for "安恒信息官方微信")*
安恒信息官方微信
==End of OCR for page 20==
