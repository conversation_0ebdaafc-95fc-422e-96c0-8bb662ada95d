# 商用密码培训课程——密码学基础知识

==Start of OCR for page 1==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA
* **Logo 2:** (Stylized S-like logo)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

# 商用密码培训课程

## ——密码学基础知识

**商密产品线**
www.dbappsecurity.com.cn 400-6059-110

*(Image: A modern office building with a large logo on top)*
==End of OCR for page 1==

==Start of OCR for page 2==
**Header:** 安恒信息 | 构建安全可信的数字世界 (DAS-Security | Build A Secure And Credible Digital World)

# 目录 (Contents)

* **01** 什么是密码?
* **02** 密码学的目标和基础技术
* **03** 密码协议及其它介绍
  ==End of OCR for page 2==

==Start of OCR for page 3==

# 01 什么是密码?

==End of OCR for page 3==

==Start of OCR for page 4==
**Header:** 什么是密码? | 安恒信息 | 构建安全可信的数字世界

## 口令与密码时常被混淆: Password并非Cryptography

### 口令: Password

*(Image: A screen showing "Password: *********" and a hand entering a PIN on an ATM keypad)*

### 密码: Cryptography

*(Image: Historical photo of military personnel with a field cipher machine, and a close-up of an Enigma-like machine)*

**密码技术与核技术、航天技术并称为国家安全三大支撑技术。**
==End of OCR for page 4==

==Start of OCR for page 5==
**Header:** 密码的定义 | 安恒信息 | 构建安全可信的数字世界

## 《密码法》: 密码是指采用特定变换的方法对信息等进行加密保护、安全认证的技术、产品和服务。

密码技术指的是把用公开的、标准的信息编码表示的信息通过一种变换手段, 将其变为除通信双方以外其他人所不能读懂的信息编码。

---

**图示描述：信息加解密流程**

1. **Mary (发送方)**: 拥有**明文**。
2. **加密操作 (Encryption)**: 使用密钥对明文进行加密。
3. **密文 (Ciphertext)**: 生成加密后的信息。
4. **解密操作 (Decryption)**: 使用密钥对密文进行解密。
5. **Rick (接收方)**: 得到恢复后的**明文**。

---

* **■ 密码技术:** 包括密码算法、密钥管理和密码协议;
* **■ 密码产品:** 指采用密码技术加密保护或者安全认证为主要功能的设备与系统;
* **■ 密码服务:** 指基于密码专业技术、技能和设施, 为他人提供集成、运营、监理等密码支持和保障的活动, 是基于密码技术和产品, 实现密码功能的行为。
  ==End of OCR for page 5==

==Start of OCR for page 6==
**Header:** 国家对密码实行分类管理 | 安恒信息 | 构建安全可信的数字世界

---

**图示描述：密码分类金字塔 (按安全级别从高到低)**

**1. 核心密码 (Top Level)**

* **定义:** 用于保护国家秘密, 其本身也属于国家秘密, 保护信息的最高密级为**绝密级**。
* **应用场景:** 一般不涉及。

**2. 普通密码 (Middle Level)**

* **定义:** 用于保护国家秘密, 其本身也属于国家秘密, 保护信息的最高密级为**机密级**。
* **应用场景:** 分保领域。

**3. 商用密码 (Bottom Level)**

* **定义:** 用于保护**不属于国家秘密的信息**。
* **应用场景:** 金融、税收、社会管理等非涉密领域。

---

**图示描述：各类密码保护的对象**

* **核心密码** 和 **普通密码** 保护 **国家秘密**。
* **商用密码** 保护 **其他信息**，包括 **个人隐私** 和 **商业秘密**。

---

==End of OCR for page 6==

==Start of OCR for page 7==
**Header:** 商用密码算法分类 | 安恒信息 | 构建安全可信的数字世界

## 商用密码算法 (也称国密算法)

国家密码管理局认定的商用密码算法, 包括SM1、SM2、SM3、SM4、SM7、SM9、ZUC等算法

| 指标                      | 算法                                             | SM1                                                                                           | SM2                                                    | SM3                                                      | SM4                                                                                        | SM7                                                                      | SM9                                                                       | ZUC |
| :------------------------ | :----------------------------------------------- | :-------------------------------------------------------------------------------------------- | :----------------------------------------------------- | :------------------------------------------------------- | :----------------------------------------------------------------------------------------- | :----------------------------------------------------------------------- | :------------------------------------------------------------------------ | :-- |
| **算法类型**        | 对称密码算法 (分组密码)                          | 非对称密码算法                                                                                | 密码杂凑算法                                           | 对称密码算法 (分组密码)                                  | 对称密码算法 (分组密码)                                                                    | 非对称密码算法 (标识密码)                                                | 对称密码算法 (序列密码)                                                   |     |
| **应用场景**        | **算法未公开**, 仅以IP核的形式存在于芯片中 | 在我国商用密码体系中被用来替换RSA算法。适用于**数字签名**、**密钥交换**等应用场景 | 适用于数据**完整性校验**、消息认证码生成及验证等 | 广泛应用于电子政务、金融等行业的**数据加密**场景中 | **算法未公开**, 适用于非接触式IC卡, 包括门禁卡、工作证、参赛证大型赛事门票、一卡通等 | 适用于物联网等新兴领域, 提供**身份认证**、**密钥协商**等功能 | 由中国科学院等单位研制, 运用于下一代移动通信4G网络LTE中的国际标准密码算法 |     |
| ==End of OCR for page 7== |                                                  |                                                                                               |                                                        |                                                          |                                                                                            |                                                                          |                                                                           |     |

==Start of OCR for page 8==

# 02 密码学的目标和基础技术

==End of OCR for page 8==

==Start of OCR for page 9==
**Header:** 密码学想要实现的四个小目标 | 安恒信息 | 构建安全可信的数字世界

## 发送者、接受者、窃听者之间的爱恨情仇

### 密码学的四个小目标

1. **机密性 (Confidentiality)**

   * **场景:** Alice向Bob发送报价“每个18元”。
   * **攻击 (信息截获):** 窃听者截获了消息，知道了报价是18元。
   * **目标:** 防止信息被窃听。
2. **完整性 (Integrity)**

   * **场景:** Alice向Bob发送的消息是“每个报价18元”。
   * **攻击 (信息篡改):** 攻击者截获并修改消息为“每个报价25元”后发给Bob。
   * **目标:** 确保信息在传输过程中未被修改。
3. **真实性 (Authenticity)**

   * **场景:** Mallory冒充Alice向Bob发送消息。
   * **攻击 (身份伪造):** Bob以为消息来自Alice，但实际上来自Mallory。
   * **目标:** 确认通信对方是其所声称的身份。
4. **不可否认性 (Non-repudiation)**

   * **场景:** Alice向Bob下单，合同约定单价20元。后来Alice抵赖，声称自己当时说的是18元。
   * **攻击 (行为抵赖):** 发送方否认自己曾经发送过某条消息。
   * **目标:** 确保发送方不能否认其发送行为。
     ==End of OCR for page 9==

==Start of OCR for page 10==
**Header:** 机密性目标的实现-加密与解密 | 安恒信息 | 构建安全可信的数字世界

信息发送者通过将明文信息加密, 以密文的方式传输至信息接收者, 即使信息在传输中被截获也无法被破译, 从而保证信息的机密性。

### 对称加密

* **图示:** 使用**同一把**对称密钥进行加密和解密。
* **特点:**
  * ■ 使用**相同**的密钥进行加密与解密;
  * ■ **优势:** 计算量小、加密速度快、加密效率高, 常用于大数据量、快速的加密、解密;
  * ■ **劣势:** 密钥不便于管理, 安全性得不到保证。
  * ■ **商密算法常用SM4**, 国际算法包括DES、3DES、AES等。

### 非对称加密

* **图示:** 加密使用接收者的**公钥**，解密使用接收者的**私钥**。
* **特点:**
  * ■ 使用**不同**的密钥（公私钥）进行加密与解密;
  * ■ **优势:** 安全性更高, 公钥可公开, 相比对称加密更容易管理密钥;
  * ■ **劣势:** 算法复杂, 效率较慢, 不适合做大数据量数据加密处理。常用于小数据量加密、数字签名、密钥交换。
  * ■ **商密算法常用SM2**, 国际算法包括RSA等。
    ==End of OCR for page 10==

==Start of OCR for page 11==
**Header:** 既要又要-混合密码系统 | 安恒信息 | 构建安全可信的数字世界

## 想同时拥有对称密码的加密效率, 也想拥有非对称密码密钥管理的便捷与安全性。

---

**图示描述：混合密码系统流程**

**发送者侧 (加密):**

1. **消息加密:** 发送者使用一个**对称密钥**对**消息明文**进行加密，生成**消息密文**。
2. **密钥加密:** 发送者使用**接收者的公钥**对**对称密钥**进行加密，生成**对称密钥密文**。
3. **组合发送:** 发送者将**消息密文**和**对称密钥密文**组合在一起发送给接收者。

**接收者侧 (解密):**

1. **密钥解密:** 接收者使用自己的**私钥**解密**对称密钥密文**，得到原始的**对称密钥**。
2. **消息解密:** 接收者使用解密出的**对称密钥**对**消息密文**进行解密，得到**消息明文**。

---

==End of OCR for page 11==

==Start of OCR for page 12==
**Header:** 完整性目标的实现-单向散列函数&消息认证码 | 安恒信息 | 构建安全可信的数字世界

单向散列函数也称为消息摘要函数、哈希(Hash)函数、杂凑函数。单向散列函数是一种可以把**任意长度**的输入消息数据转化为**固定长度**的输出数据的密码算法, 通常用来做数据完整性的判定, 即对数据进行散列计算然后比较散列值是否一致。**散列运算过程不需要密钥。**

### 单项散列函数

* **图示:** 任意长度的**消息**输入到**单向散列函数**中，输出固定长度的**散列值**。
* **特点:**
  * ■ 可以根据任意长度的消息计算出固定长度的散列值;
  * ■ 能够快速计算出散列值;
  * ■ 消息不同散列值也不同;
  * ■ 具备不可逆性, 无法通过散列值反算出消息。

### 消息认证码 (MAC)

* **图示:** **消息**和**密钥**一同输入到**消息认证码函数**中，输出**MAC值**。
* **特点:**
  * ■ 一种确认完整性并进行认证的技术, 可以认为是一种与密钥相关联的单向散列函数;
  * ■ 需要持有共享密钥才能计算MAC值;
  * ■ **HMAC**是一种使用单向散列函数来构造消息认证码的方法 (H指Hash), HMAC才符合商密对完整性校验的要求 (**SM3**)。
    ==End of OCR for page 12==

==Start of OCR for page 13==
**Header:** 不可否认性目标的实现-数字签名 | 安恒信息 | 构建安全可信的数字世界

签名者通过使用**私钥**对待签名数据的散列值做密码运算得到数字签名, 该结果只能用签名者的**公钥**进行验证, 用于确认待签名数据的**完整性**和签名行为的**抗抵赖性**。

---

**图示描述：数字签名与验证流程**

**1. 签名 (由签名者完成)**

* **私钥仅签名者持有**
* **步骤:**
  1. 对原始**消息**进行**散列计算**，得到**散列值**。
  2. 使用自己的**私钥**对**散列值**进行加密，生成**数字签名**。
  3. 将原始**消息**和**数字签名**一同发送给接收者。

**2. 验签 (由接收者完成)**

* **步骤:**
  1. 接收者收到消息和数字签名。
  2. 对收到的**消息**使用相同的**散列算法**进行计算，得到一个新的**散列值A**。
  3. 使用**签名者的公钥**对**数字签名**进行解密，得到一个**散列值B**。
  4. **对比**散列值A和散列值B，如果两者相同，则验签成功。

**引出问题:** 怎么证明公钥是你（签名者）的呢？ -> 需要数字证书。
------------------------------------------------------------

==End of OCR for page 13==

==Start of OCR for page 14==
**Header:** 真实性目标的实现-数字证书 | 安恒信息 | 构建安全可信的数字世界

**数字证书**是由**CA机构发放并经其认证的**, 包含拥有者身份信息以及公钥相关信息的一种电子文件, 可以用来证明数字证书持有者的真实身份, 是各类实体(个人、商户、企业、单位等)在网上进行信息交流及商务活动的身份证明, **解决相互间的信任问题**。

(数字证书解决了公钥真实性的问题, 确保你拿到的A的公钥确实是A的公钥, 而不是B仿冒的)

---

**左侧：数字证书信息**
*(Image: A screenshot of a Windows certificate details dialog, showing fields like Version, Serial Number, Issuer, Subject, etc., alongside a sample of a Chinese ID card.)*

* **主要内容:** 包含证书持有者信息(Subject)、颁发者信息(Issuer)、有效期(Period of Validity)、公钥(Subject's Public Key)以及颁发者对证书的签名(Signature)。

**右侧：数字证书的存储**
*(Image: A USB Key (U盾).)*

* **存储信息:**
  * 用户的证书
  * 用户的私钥
  * CA机构根证书
* **存储设备:**
  * 硬盘
  * 智能IC卡
  * TF卡/SD卡
  * 智能密码钥匙(USB Key)

---

**电子政务电子认证服务机构目录:** https://www.oscca.gov.cn/app-zxfw/xzspsx/dzzwdzrzfuwujigouml.jsp?channel_code=c100144
==End of OCR for page 14==

==Start of OCR for page 15==
**Header:** 数字证书的申请/验证 | 安恒信息 | 构建安全可信的数字世界

### 数字证书申请

**流程描述:**

1. **证书申请者:** 自己生成密钥对A（公钥A和私钥A）。
2. **申请者 to CA:** 保留私钥A，将公钥A及其他参数发送给CA（证书颁发机构）。
3. **CA:** 使用自己的CA私钥，对申请者的信息（包括公钥A）进行签名，生成证书。
4. **CA to 申请者:** 将生成的证书发回给申请者。
   *(流程图中还包含加密证书的申请，流程类似，由CA生成密钥对B并加密传输私钥B)*

### 数字证书验证

**流程描述:**

1. **用户(Client)向服务器(Server)请求证书:**
   * **1. 申请认证(证书):** 用户发起连接。
   * **4. 返回证书:** 服务器返回自己的数字证书。
2. **用户验证服务器证书:**
   * **5. 验证证书:**
     * **计算摘要:** 用户计算证书中明文信息（INFO）的摘要 D_cal = Hash(INFO)。
     * **解密签名:** 用户使用CA的公钥解密证书中的签名 D_pem = Dec_by_pub_CA[Sig]。
     * **对比摘要:** 对比 D_cal 和 D_pem 是否相等。
     * **验证其他信息:** 如域名、有效期、是否被吊销等。
3. **证书的信任链:**
   * **根证书 (Root Certificate):** 自签名，是信任的起点。
   * **中间证书 (Intermediate Certificate):** 由根证书签发，用于签发服务器证书。
   * **服务器证书 (Server Certificate):** 由中间证书签发。
   * **验证过程:** 浏览器验证服务器证书时，会追溯其签名者（中间证书），再追溯中间证书的签名者（根证书），直到找到一个内置于操作系统或浏览器的可信根证书为止。
     ==End of OCR for page 15==

==Start of OCR for page 16==
**Header:** 数字证书的基础——PKI公钥基础设施 | 安恒信息 | 构建安全可信的数字世界

**公钥基础设施 (Public-Key Infrastructure)** 是为了能够更有效地运用公钥而制定的一系列规范和规格的总称。公钥基础设施一般根据其英语缩写而简称为**PKI**。

---

**图示描述：PKI层级结构**

* **CA (证书认证机构):** 位于信任链顶端。
* **RA (证书注册审批机构):** CA的下级机构。
* **受理点:** RA的下级，直接面向用户。

---

### 证书认证机构 (CA)

**负责发放和管理数字证书的权威机构, 主要实现: 证书发放、证书更新、证书撤销和证书验证。**

* 对下级机构进行认证和鉴别
* 产生和管理下属机构的证书
* 接收和认证RA证书请求
* 签发和管理证书
* 发布证书CRL(证书吊销列表)

### 证书注册审批机构 (RA)

**负责证书申请者的信息录入、审核以及证书发放等工作。**

* 进行用户身份信息的审核, 确保其真实性
* 本区域用户身份信息管理的维护
* 数字证书的下载
* 数字证书的发放和管理
* 登记黑名单
  ==End of OCR for page 16==

==Start of OCR for page 17==
**Header:** 我们会如何跟CA打交道? | 安恒信息 | 构建安全可信的数字世界

### X.509数字证书格式

| 域 (Field)                     | 含义 (Meaning)                               |
| :----------------------------- | :------------------------------------------- |
| **Version**              | 证书版本号, 不同版本的证书格式不同           |
| **Serial Number**        | 序列号, 同一身份验证机构签发的证书序列号唯一 |
| **Algorithm Identifier** | 签名算法, 包括必要的参数                     |
| **Issuer**               | 身份验证机构的标识信息                       |
| **Period of Validity**   | 有效期                                       |
| **Subject**              | 证书持有人的标识信息                         |
| **Subject's Public Key** | 证书持有人的公钥                             |
| **Signature**            | 身份验证机构对证书的签名                     |

*数字证书的格式一般采用X.509国际标准。X.509是广泛使用的证书格式之一...*

### 与CA交互的流程

**1. 申请 (Application)**

* **用户端 -> RA:** 用户通过安全载体向RA提交证书信息（用户信息、签名公钥）。
* **RA -> CA:** RA进行申请、录入、审核后，向CA申请签发证书。
* **CA -> KM (密钥管理中心):** CA向KM申请加密密钥对。
* *(整个流程经过多方验证(Verify))*

**2. 下发 (Issuance)**

* **CA -> RA:** CA签发签名证书和加密证书，形成证书数据。
* **RA -> 用户端:** RA将证书及密钥数据写入用户载体（如USB Key）。

**3. 查询/验证 (Query/Validation)**

* **用户端/应用系统 -> LDAP/OCSP:** 应用程序通过LDAP（目录访问协议）或OCSP（在线证书状态协议）查询证书状态（是否有效/吊销）。
  ==End of OCR for page 17==

==Start of OCR for page 18==

# 03 密码协议及其它

==End of OCR for page 18==

==Start of OCR for page 19==
**Header:** 随机数-不可预测性的源头 | 安恒信息 | 构建安全可信的数字世界

## 随机数分为真随机数和伪随机数。伪随机数又分为弱伪随机数和强伪随机数:

**真随机数**

* 通过**硬件实现**, 同时具备随机性、不可推测和不可重复3个特点, **可用于密码技术**。
* 真随机数的生成是基于**物理现象**完成的, 例如物理噪声源, 掷骰子、抛硬币。
* 任何人无法通过软件算法或其他方法得知下一个随机数（或者随机数下一位）是什么。

**强伪随机数**

* 通过**软件或硬件实现**, 能够满足随机性和不可预测性, **可用于密码技术**。
* 例如: 通过获取敲击键盘的数据作为随机数种子进而生成一组强伪随机数, 因为击打键盘存在不确定性, 所以生成的随机数也更接近与真正的随机数。

**弱伪随机数**

* 通过**纯软件算法**, 按照一定的规律生成一个随机值, **不可用于密码技术**。
* 具备一定的随机性, 并不具备不可预测性, 即并不是一个真正的随机数。
* 例如: 通过MCU的时间作为种子去生成一个随机数, 因为时间是不停的变量, 所以生成的随机数也不停变化。但时间的变化终究有规律可循。

**随机数发生器**

* 可分为真随机数发生器、伪随机数发生器。又可细分为硬件真随机数发生器和硬件伪随机数发生器。其中只有**硬件真随机数发生器**对于嵌入式系统应用才是真正安全有效的。
* 加密芯片属于电子元器件, 其运行过程会产生**高斯白噪声**, 以其作为信息熵资源, 产生真随机数。
  ==End of OCR for page 19==

==Start of OCR for page 20==
**Header:** 经典密码协议-SSL/TLS/TLCP | 安恒信息 | 构建安全可信的数字世界

### SSL/TLS的历史

* **SSL 1.0 (1994年):** NetScape公司提出，因存在严重安全漏洞，**未公开**。
* **SSL 2.0 (1995年2月):** 公开发布，后被发现存在数个严重安全漏洞，于2011年弃用。
* **SSL 3.0 (1996年):** 得到大规模应用，2014年发现设计缺陷，于2015年弃用。
* **TLS 1.0 (1999年, RFC 2246):** 被IETF纳入标准化，改名TLS，与SSL 3.0差异较小。
* **TLS 1.1 (2006年4月, RFC 4346):** 修复bug，增加参数。
* **TLS 1.2 (2008年8月, RFC 5246):** 更多扩展和算法改进。
* **TLS 1.3 (2018年8月, RFC 8446):** 在TLS 1.2基础上调整、扩展了较多功能，减少时延，完全前向安全。

### 国密SSL/TLCP

* **GMSSL™:** 国密SSL标准。
* **信息安全技术 传输层密码协议 (TLCP):** Information security technology—Transport layer cryptography protocol (TLCP)
  ==End of OCR for page 20==

==Start of OCR for page 21==
**Header:** 关于SSL握手还想补充一些 | 安恒信息 | 构建安全可信的数字世界

TLS握手的核心目的在于**密钥交换**，服务器与客户端“协商”得出主密钥（所谓“协商”就是互相交换几个随机数，你说一个数，我说一个数，最后根据大家的数计算出一个结果）。密钥交换有通过**非对称加密（RSA/ECC）**的方式也有通过**密钥交换协议（DHE/ECDHE）**的方式，但最终计算主密钥的公式都是相同的:

`client random + server random + pre-master = master secret`

前两个随机数完全明文，保密的关键在于 **pre-master**。

* **在加密方式中 (如RSA):**

  * `pre-master` 是单纯的由客户端生成, 通过服务器的公钥加密后发给服务器, 服务器使用私钥解密拿到 `pre-master`。
  * 一旦服务器的私钥被破解, 主密钥就会被攻击者算出, 并且会导致**过往的**主密钥泄漏 (不具备“**前向安全性**”)。
* **在密钥交换协议中 (如ECDHE):**

  * 服务器生成一个“椭圆曲线的公钥” `Server Params`，使用私钥签名后将其发送给客户端。
  * 客户端也生成一个“椭圆曲线的公钥” `Client Params`，使用服务器的公钥加密后发给服务器。
  * 而私钥 `a` 和 `b` 由服务器和客户端分别保管。
  * 随后客户端与服务器分别在本地计算 `pre-master`:
    * 在客户端上: `Server Params ^ b % P = pre-master`
    * 在服务器上: `Client Params ^ a % P = pre-master`
  * 由此可见, 在ECDHE中, 即使破解了服务器的私钥, 拿到的也只是客户端发送的 `Client Params`, 没有椭圆曲线的私钥 `a`和 `b`, 就无法计算出 `pre-master`。
  * **总结:** 想要破解对称加密过程中的密钥:
    1. 对于**RSA**方式需要破解**一次**私钥 (服务器的私钥)。
    2. 对于**ECDHE**需要破解**两次**私钥 (服务器私钥 + 椭圆曲线私钥), 且椭圆曲线私钥**每次握手都会随机生成**, 保证了**前向安全性**。

*(注：页面左侧是一个详细的9步SSL握手流程图，以上文本是对其核心原理的提炼和解释。)*
==End of OCR for page 21==

==Start of OCR for page 22==
**Header:** 经典密钥交换协议-DH算法 | 安恒信息 | 构建安全可信的数字世界

---

**图示描述：Diffie-Hellman (DH) 密钥交换（油漆桶比喻）**

1. **公共参数 (Common paint):** Alice 和 Bob 首先协商一个公开的、相同的底色（算法参数）。
2. **生成私钥 (Secret colours):** Alice 和 Bob 各自选择一个私密的颜色（私钥）。
3. **生成公钥 (Public transport):** 双方将自己的私密颜色与公共底色混合，得到一个新的公开颜色（公钥）。
4. **交换公钥:** Alice 和 Bob 交换他们混合后的公开颜色。
5. **生成共享密钥 (Common secret):** 双方将收到的对方的公开颜色与自己的私密颜色混合。由于混合顺序不影响最终结果，他们会得到一个完全相同、但窃听者无法生成的最终颜色（共享密钥）。

---

先双方协商一个相同的底色（算法参数），然后各自生成自己私有的颜色（相当于私钥），并通过混合得到对应的公有颜色（相当于公钥）。随后双方交换各自的公有颜色，并与自己的私钥颜色混合，最终协商出一个相同的颜色（即交换的密钥）。窃听者就算得到了双方交换的这些信息，也无法生成相同的密钥，**求解离散对数问题的困难度**保证了DH算法的安全性。

但是**DH密钥协商算法**中, 服务器的公私密钥是固定的, 只有客户端的公钥是会话时随机生成, 所以安全隐患很大 (没有**前向安全性**, 服务端私钥一旦泄露, 以往的会话记录都会被破解)。

**DHE (Ephemeral Diffie-Hellman) 密钥交换**时, 服务器私钥没有参与进来, 而是客户端和服务器端同时生成**临时的**随机数。也就是说, 私钥即使泄漏, 也不会导致会话加密密钥S被第三方解密, 因此和**ECDHE**一样也具备**前向安全性**。
==End of OCR for page 22==

==Start of OCR for page 23==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA
* **Logo 2:** (Stylized S-like logo)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

## 联系我们 (Contact Us)

浙江省杭州市滨江区西兴街道联慧街188号安恒大厦
www.dbappsecurity.com.cn
400-6059-110

*(Image: QR Code for "安恒信息官方微信")*
安恒信息官方微信

*(Image: The same office building from the first page)*
==End of OCR for page 23==
