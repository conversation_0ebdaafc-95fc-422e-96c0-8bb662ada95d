# 商用密码培训课程——密评基本要求

==Start of OCR for page 1==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

# 商用密码培训课程

## ——密评基本要求

**商密产品线**
www.dbappsecurity.com.cn 400-6059-110

*(Image: A modern office building, 安恒大厦)*
==End of OCR for page 1==

==Start of OCR for page 2==
**Header:** 安恒信息 | 构建安全可信的数字世界 (DAS-Security | Build A Secure And Credible Digital World)

# 目录 (Contents)

* **01** 密评基础信息介绍
* **02** 信息系统密码应用基本要求讲解
  ==End of OCR for page 2==

==Start of OCR for page 3==

# 01 | 密评基础信息介绍

==End of OCR for page 3==

==Start of OCR for page 4==
**Header:** 密评的定义 | 安恒信息 | 构建安全可信的数字世界

**商用密码应用安全性评估 (简称“密评”)**：是指按照有关法律法规和标准规范, 对网络与信息系统使用商用密码技术、产品和服务的**合规性、正确性、有效性**进行检测分析和评估验证的活动。

---

* **合规性**

  * 使用的密码算法、密码协议、密钥管理符合国家法律法规和标准规定, 密码产品或服务经过国家密码管理局核准和认证机构认证合格。
  * **反例:** 使用弱密码，数据易被破解。
* **正确性**

  * 密码算法、密码协议、密钥管理、密码产品或服务使用正确, 即按密码相关的国家和行业标准进行正确设计和实现, 密码产品和服务的部署、使用正确。
  * **反例:** 未使用密码，数据裸奔。
* **有效性**

  * 密码保障系统在系统运行过程中能够发挥实际效用, 保障信息系统的安全需求, 解决信息系统面临的安全问题。
  * **反例:** 密码应用没有防护效果。
    ==End of OCR for page 4==

==Start of OCR for page 5==
**Header:** 密评发展历程 | 安恒信息 | 构建安全可信的数字世界

密评最早于 2007年提出, 经过十余年的积累, 密评制度体系不断成熟, 其发展主要经历了以下阶段:

### 时间线

* **制度奠基期 (2007.11 - 2016.8)**

  * **2007年11月27日:** 国家密码管理局印发11号文件《信息安全等级保护商用密码管理办法》, 要求信息安全等级保护商用密码测评工作由国家密码管理局指定的测评机构承担。
  * **2009年12月15日:** 国家密码管理局印发管理办法实施意见, 进一步明确了与密码测评有关的要求。
* **再次集结期 (2016.8 - 2017.5)**

  * 国家密码管理局成立起草小组, 研究起草《商用密码应用安全性评估管理办法(试行)》。
  * **2017年4月22日:** 正式印发《关于开展密码应用安全性评估试点工作的通知》(国密局(2017)138号文), 在七省五行业开展密评试点。
* **体系建设期 (2017.5 - 2017.10)**

  * 国家密码管理局成立密评领导小组, 研究确定了密评体系总体架构, 并组织有关单位起草14项制度文件。
  * **2017年9月27日:** 国家密码管理局印发《商用密码应用安全性测评机构管理办法(试行)》、《商用密码应用安全性测评机构能力评审实施细则(试行)》、《信息系统密码应用基本要求》和《信息系统密码测评要求(试行)》, 密评制度体系初步建立。
* **密评试点开展期 (2017.10 - 2021.3)**

  * **2017年10月:** 商用密码应用安全性评估试点工作开展; 经过评审, **第一批共有10家测评机构**符合能力要求。
  * **2019年上半年:** 对第一批密评试点做了评审总结, 对参与试点的27家机构进行能力再评审, 择优选出16家扩大试点, 对另11家机构给予6个月能力提升整改期。
  * **2019年10月:** 开启第二批密评试点工作。
  * **2020年7月:** 评估试点机构增加至24家。
* **推广发展期 (2021.3 - 至今)**

  * **2021年3月:** 商用密码应用安全性评估的关键标准**GB/T 39786—2021《信息安全技术信息系统密码应用基本要求》正式发布**, 并于2021年10月1日正式实施。
  * **2021年6月:** 国家密码管理局第42号公告, 发布最新的《商用密码应用安全性评估试点机构目录》, **明确48家商用密码应用安全性评估试点机构**。
    ==End of OCR for page 5==

==Start of OCR for page 6==
**Header:** 密评相关标准一览 | 安恒信息 | 构建安全可信的数字世界

目前我国已形成较为完善的密评标准体系, 包括法律法规、国家标准、行业标准、指导性文件等, 有效助力密评工作的开展和推广。

### 法律

* 中华人民共和国网络安全法
* 中华人民共和国密码法
* 中华人民共和国数据安全法

### 法规/政策

* 商用密码管理条例 (2023年修订版)
* 商用密码应用安全性评估管理办法
* 商用密码检测机构管理办法

### 国家标准 (升级关系)

* **行业标准 (旧):** GM/T 0054-2018 信息系统密码应用基本要求
* **国家标准 (新):** GB/T 39786-2021 信息安全技术 信息系统密码应用基本要求 (升级)

---

* **行业标准 (旧):** GM/T 0115-2021 信息系统密码应用测评要求
* **国家标准 (新):** GB/T 43206-2023 信息安全技术 信息系统密码应用测评要求 (升级)

---

* **行业标准 (旧):** GM/T 0116-2021 信息系统密码应用测评过程指南
* **国家标准 (新):** GB/T 43207-2023 信息安全技术 信息系统密码应用设计指南

### 指导性文件

* 信息系统密码应用高风险判定指引
* 商用密码应用安全性评估量化评估规则 (2023版)
* 商用密码应用安全性评估FAQ (第三版)
  ==End of OCR for page 6==

==Start of OCR for page 7==
**Header:** 不做密评的后果 | 安恒信息 | 构建安全可信的数字世界

《密码法》、《商用密码管理条例》对关键信息基础设施开展密评提出了明确的要求。政府、运营商、医疗等行业在法律法规的指导下也相继推出相关行业政策, 促进密评有序开展。

* **《中华人民共和国密码法》**

  * **【施行日期】** 2020年1月1日
  * **【关键内容】**
    > **第三十七条** 关键信息基础设施的运营者违反本法第二十七条第一款规定, **未按照要求使用商用密码, 或者未按照要求开展商用密码应用安全性评估的**, 由密码管理部门**责令改正, 给予警告**; 拒不改正或者导致危害网络安全等后果的, **处十万元以上一百万元以下罚款**, 对直接负责的主管人员**处一万元以上十万元以下罚款**。......
    >
* **《商用密码管理条例》**

  * **【施行日期】** 2023年7月1日
  * **【关键内容】**
    > **第六十条** 关键信息基础设施的运营者违反本条例第三十八条、第三十九条规定, **未按照要求使用商用密码, 或者未按照要求开展商用密码应用安全性评估的**, 由密码管理部门**责令改正, 给予警告**; 拒不改正或者有其他严重情节的, **处10万元以上100万元以下罚款**, 对直接负责的主管人员**处1万元以上10万元以下罚款**。
    > **第六十二条** 网络运营者违反本条例第四十一条规定, **未按照国家网络安全等级保护制度要求使用商用密码保护网络安全的**, 由密码管理部门**责令改正, 给予警告**; 拒不改正或者导致危害网络安全等后果的, **处1万元以上10万元以下罚款**, 对直接负责的主管人员**处5000元以上5万元以下罚款**。
    >
* **《国家政务信息化项目建设管理办法》(国办发[2019]57号)**

  * **【发布日期】** 2019年12月30日
  * **【发布单位】** 国务院办公厅
  * **【相关内容】** 项目建设单位应当落实国家密码管理有关法律法规和标准规范的要求, **同步规划、同步建设、同步运行密码保障系统并定期进行评估**。对于不符合密码应用和网络安全要求, 或者存在重大安全隐患的政务信息系统, **不安排运行维护经费, 项目建设单位不得新建、改建、扩建政务信息系统**。
    ==End of OCR for page 7==

==Start of OCR for page 8==
**Header:** 密评工作标准流程 | 安恒信息 | 构建安全可信的数字世界

对于使用商用密码保护的网络与信息系统, 项目建设单位应同步规划、同步建设、同步运行密码保障系统并定期进行商用密码安全性评估, 即信息系统中的密码保障系统应做到**“三同步一评估”**。信息系统的密码应用与安全性评估贯穿于系统的规划、建设和运行阶段, 其实施过程如图所示:

### 规划阶段

1. **分析系统现状, 明确系统密码应用需求**
2. 根据密码需求, **编制《密码应用方案》**
3. **组织专家或委托测评机构对《密码应用方案》评估**
4. **《密码应用方案》评估通过**
   * *说明:* 密码应用方案评估结果须报所属密码管理局备案, 同时也是项目立项的重要依据。

### 建设阶段

1. **按照密码应用方案建设实施**
2. **(系统整改)** -> **委托测评机构进行商用密码应用安全性评估**
3. **信息系统通过评估** -> **取得《商用密码应用安全性评估报告》**
   * *说明:* 责任单位在验收前委托检测机构进行密码应用测评。项目通过密码应用测评是项目验收的必要条件。

### 运行阶段

1. **(系统整改)** -> **定期开展系统商用密码应用安全性评估**
2. **(必要时)修改密码应用方案**
3. **对《密码应用方案》进行评估** -> **通过评估的《密码应用方案》**
   * *说明:* 每年至少开展一次密码应用测评, 并将评估结果报所属密码管理局备案。通过密评是项目运维经费审批的重要条件。
     ==End of OCR for page 8==

==Start of OCR for page 9==
**Header:** 密评参与方关系链路 | 安恒信息 | 构建安全可信的数字世界

密评工作在实际开展中往往涉及多方参与, 常见的关系链路如下:

* **密码厂商**

  * **职责:**
    1. 密码应用方案、实施方案等编制
    2. 产品安装实施
    3. 协助开展系统密码改造工作
    4. 配合密码测评工作
  * **交互:**
    * -> (提供密码应用方案) -> **设计单位/外部专家**
    * -> (输出方案、设备实施) -> **业主单位/系统集成商**
* **设计单位 (外部专家)**

  * **职责:**
    1. 方案调研
    2. 可行性研究报告
    3. 方案设计
    4. 第三方专家评审
  * **交互:**
    * <- (委托设计、评审) - **业主单位**
    * -> (输出方案、报告) -> **业主单位**
* **业主单位 (系统开发商)**

  * **职责:**
    * 核心参与方
  * **交互:**
    * -> (委托方案编制) -> **密码厂商**
    * -> (委托测评) -> **测评机构**
* **系统集成商**

  * **职责:** 系统合规性开发改造
  * **交互:**
    * <- (输出方案、设备实施) - **密码厂商**
* **测评机构**

  * **职责:** 方案测评与实施测评, 提供评估报告
  * **交互:**
    * <- (委托测评) - **业主单位**
    * -> (方案及实施测评报告同步) -> **密码管理局**
* **密码管理局 (监管单位)**

  * **职责:**
    1. 对测评机构进行监督管理
    2. 对测评系统进行抽查
  * **交互:**
    * -> (监督检查) -> **测评机构**
    * <- (盖章备案) - **测评机构**

**最终目标:** 密码应用项目成功
==End of OCR for page 9==

==Start of OCR for page 10==
**Header:** 等保和密评的关联与区别 | 安恒信息 | 构建安全可信的数字世界

网络安全等级保护是国家网络安全保障的基本制度、基本策略、基本方法。在GB/T 22239基本要求中对**身份鉴别、数据完整性、数据保密性**等相关内容要求应采用密码技术保证安全性。GB/T 39786在等保基本要求的基础上, 对信息系统应用密码技术提出更细致的要求, 形成二者**既相互补充, 又可相互独立实施**的格局。

等保测评和商用密码应用安全性评估作为保障信息系统安全的两个有利抓手, 相辅相成、互相促进, 共同构建了网络空间下信息系统的“免疫体系”。

### 测评对象范围

* **等保测评对象:** 基本覆盖了全部的网络和信息系统。
* **密评对象:** 包括关键信息基础设施, 以及各行业自行定义的重要业务系统。
* **关系:** 关键信息基础设施**一定**是等保测评和密评的评估对象。

> **《密码法》第二十七条:** 商用密码应用安全性评估应当与关键信息基础设施安全检测评估、网络安全等级测评制度相衔接, **避免重复评估、测评**。
> ==End of OCR for page 10==

==Start of OCR for page 11==

# 02 | 信息系统密码应用基本要求讲解

==End of OCR for page 11==

==Start of OCR for page 12==
**Header:** 密评的等级划分和要求组成 | 安恒信息 | 构建安全可信的数字世界

GB/T39786参照GB/T22239的等保对象应具备的基本安全保护能力要求, 对信息系统密码应用划分为自低向高的五个等级, 用第一级-第五级的方式表示, 即**等保三级的系统, 要按照密码应用三级的要求进行建设和评估**。

*(Image: Cover of GB/T 39786-2021 standard document)*

### 要求组成

* **通用要求**

  * 密码算法、密码技术、密码产品和服务需符合法律、法规以及相关标准要求, **所有级别都需要遵循**。
* **技术要求**

  * 从**物理和环境安全、网络和通信安全、设备和计算安全、应用和数据安全**四个层面提出密码应用技术要求。
* **管理要求**

  * 从信息系统的**管理制度、人员管理、建设运行和应急处置**四个方面提出密码应用管理要求。
* **密钥管理**

  * 将密钥生存管理周期所涉及的技术环节移至资料性附录B, **不单独作为要求**。
    ==End of OCR for page 12==

==Start of OCR for page 13==
**Header:** 密评的分值分布和通过标准 | 安恒信息 | 构建安全可信的数字世界

密评采用打分制, 总分**100分**由**技术要求70分**和**管理要求30分**组成。测评过程中将根据量化评估规则, 从密码使用有效性(D)、密码算法/技术合规性(A)、密钥管理安全(K)三个维度进行逐项打分和风险分析, 最终形成测评结论。**符合**与**基本符合**均属于通过密评。

### 测评结论标准

* 得分**100分**且没有任何风险项, 测评结论为**符合**;
* 得分达到**60分**且没有高风险项, 测评结论为**基本符合**;
* 得分**未达到60分**或者**存在高风险项**, 测评结论为**不符合**。

### 分值分布

* **GB/T39786 信息系统密码应用基本要求**
  * **技术要求 (70分)**
    * 物理和环境安全 (10分)
    * 网络和通信安全 (20分)
    * 设备及计算安全 (10分)
    * 应用和数据安全 (30分)
  * **管理要求 (30分)**
    * 管理制度 (8分)
    * 人员管理 (8分)
    * 建设运行 (8分)
    * 应急处置 (6分)
      ==End of OCR for page 13==

==Start of OCR for page 14==
**Header:** 密评要求详解——通用要求 | 安恒信息 | 构建安全可信的数字世界

通用要求适用于第一级到第五级, 即所有信息系统均需要符合。通用要求共计三项:

| 通用要求                                                                                                          | 相关说明                                                                                                                                                                                                                                             |
| :---------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **a) 信息系统中使用的`<u>`密码算法`</u>`应符合法律、法规的规定和密码相关国家标准、行业标准的有关要求;** | `<ul><li>`需要使用合规的商密算法, 如SM2、SM3、SM4等, 或者安全的国际算法, 如AES、RSA2048、SHA-512等;`</li><li>`淘汰存在安全风险的算法, 如DES、MD5、SHA-1、RSA1024及以下等。`</li></ul>`                                                         |
| **b) 信息系统中使用的`<u>`密码技术`</u>`应遵循密码相关国家标准和行业标准;**                             | `<ul><li>`如采用符合GM/T 0024-2014《SSL VPN 技术规范》、GM/T 0030-2014《服务器密码机技术规范》的产品;`</li><li>`避免使用有安全问题的密码技术, 如SSH1.0、SSL2.0等, 以及安全性未知的密码技术, 如自行设计的密码通信协议、加密方式等。`</li></ul>` |
| **c) 信息系统中使用的`<u>`密码产品、密码服务`</u>`应符合法律法规的相关要求。**                          | `<ul><li>`密码产品具备商用密码产品认证证书、密码服务具有相关许可证书;`</li><li>`避免使用存在安全漏洞或无法提供安全性证据的密码产品。`</li></ul>`                                                                                               |

*(Images: Sample of a "商用密码产品认证证书" and an "电子认证服务许可证")*
==End of OCR for page 14==


==Start of OCR for page 15==
**Header:** 密评要求详解——技术要求(物理和环境安全) | 安恒信息 | 构建安全可信的数字世界

以常用的密评三级要求为例, 物理和环境安全层面共计10分, 包含5项技术要求, 其中2项为产品资质要求。

### 技术要求

* a) 宜采用密码技术进行物理访问身份鉴别, 保证重要区域进入人员身份的真实性; **(高风险项)**
* b) 宜采用密码技术保证电子门禁系统进出记录数据的存储完整性;

### 常用应对方案

上述两项技术要求通常使用具备商密资质的**国密门禁系统**进行响应。

1. **国密CPU卡**和**国密门禁读卡器**通过采用基于**SM4国密算法**的对称加解密技术, 实现用户身份鉴别;
2. **PCI-E密码卡**配合**门禁日志审计系统**, 采用基于**SM3的HMAC**技术, 实现对电子门禁进出记录数据的完整性保护。

#### 国密门禁系统架构图

* **组件:** 国密CPU卡, 国密读卡器, 门禁控制器, 发卡器, 监控主机, PCI-E密码卡。
* **网络:** 各组件通过IP网络连接。
  ==End of OCR for page 15==

==Start of OCR for page 16==
**Header:** 密评要求详解——技术要求(物理和环境安全) | 安恒信息 | 构建安全可信的数字世界

### 技术要求

* c) 宜采用密码技术保证视频监控音像记录数据的存储完整性。

### 常用应对方案

上述技术要求通常使用具备商密资质的**国密视频监控系统**进行响应。通过**国密NVR**和**PCI-E密码卡** (配合视频播放客户端软件使用), 采用基于**SM3的HMAC**技术, 实现对音像记录数据的完整性保护。

#### 国密视频监控系统架构图

* **组件:** 国密摄像头, IP网络设备, 国密NVR, 监控主机, PCI-E密码卡。

---

### 物理和环境安全层面小结:

* 物理和环境安全层面通过使用具有商密认证资质的**电子门禁**和**视频监控系统**, 10分基本可以拿满, 也是技术要求里相对好拿分的地方, 因此在密评项目中推荐客户做相关的改造。
* 除了使用国密门禁和视频监控系统, 也可以**使用密码机**对已有门禁和监控系统做改造, 但这种方式工作量比较大, 不是首选方案。
* 如果客户实际情况无法对物理机房做改造, 则需要注意补充高风险项缓解措施, 避免无法通过测评。
  ==End of OCR for page 16==

==Start of OCR for page 17==
**Header:** 密评要求详解——技术要求(网络和通信安全) | 安恒信息 | 构建安全可信的数字世界

以常用的密评三级要求为例, 网络和通信安全层面共计20分, 包含7项技术要求, 其中2项为产品资质要求。

### 技术要求

* a) 应采用密码技术对通信实体进行身份鉴别, 保证通信实体身份的真实性; **(高风险无缓解措施)**
* b) 宜采用密码技术保证通信过程中数据的完整性;
* c) 应采用密码技术保证通信过程中重要数据的机密性; **(高风险项)**

### 常用应对方案

上述三项要求中包括两项高风险项, 是网络和通信安全层面的重点。实际测评中将跨网络访问的通信信道作为测评对象进行测评打分, 常见的通信信道可以总结为**业务通道、运维通道、灾备通道**三种:

#### 一、业务通道

* 对于**B/S架构**且**用户范围较广**的业务系统, 通常采用合规机构签发的**国际算法SSL站点证书**对应用系统进行改造, 通过SSL站点证书建立安全的https链接, 能满足基本的单向身份鉴别、通信数据机密性和完整性保护, **但国密算法合规性缺失, 部分得分**。
  * **架构图 (代理模式):**
    1. **用户终端 (普通浏览器)** -> (HTTPS) -> **安全认证网关 (配置非国密站点证书)**
    2. **安全认证网关** -> (HTTP) -> **应用服务器**

==End of OCR for page 17==

==Start of OCR for page 18==
**Header:** 密评要求详解——技术要求(网络和通信安全) | 安恒信息 | 构建安全可信的数字世界

#### 一、业务通道 (续)

* 对于**B/S架构**但面向的**用户范围较小**的业务系统, 通常采用合规机构签发的**国密SSL站点证书**对应用系统进行改造, 并在客户端通过**国密浏览器**进行访问, 能够完全满足密评要求。

  * **架构图 (代理模式):**
    1. **用户终端 (国密浏览器)** -> (国密HTTPS) -> **安全认证网关 (配置国密站点证书)**
    2. **安全认证网关** -> (HTTP) -> **应用服务器**
  * *注: 当需要双向身份认证时，用户终端可配合USBKey使用。*
* 对于**无法加载国密SSL站点证书**(如C/S业务、非标准端口的B/S业务等)的应用系统, 在用户范围较小的场景下可以采用具备商密资质的**SSL VPN安全网关**, 通过国密SSL VPN隧道满足身份鉴别、通信数据机密性和完整性保护。

  * **架构图:**
    1. **用户终端** -> (国密SSL VPN隧道) -> **SSL VPN网关**
    2. **SSL VPN网关** -> (内部协议) -> **应用服务器**

#### 二、运维通道

* 运维通道场景下, 运维人员通过使用具备商密资质的**USBkey**, 与**SSL VPN安全网关**建立的国密SSL VPN隧道访问应用, 满足身份鉴别、通信数据机密性和完整性保护。
  * **架构图:**
    1. **运维终端 + USB-Key** -> (国密SSL VPN隧道) -> **SSL VPN网关**
    2. **SSL VPN网关** -> (内部协议) -> **堡垒机** -> **应用系统**
       ==End of OCR for page 18==

==Start of OCR for page 19==
**Header:** 密评要求详解——技术要求(网络和通信安全) | 安恒信息 | 构建安全可信的数字世界

#### 三、灾备通道

* 灾备通道场景下, 通常采用具备商密资质的**IPsec VPN网关**, 实现通信实体身份鉴别、通信数据机密性和完整性保护。
  * **架构图:**
    1. **主业务系统** <-> **IPSec VPN网关** <- (国密IPSec VPN隧道) -> **IPSec VPN网关** <-> **备业务系统**

### 技术要求

* d) 应采用密码技术保证网络边界访问控制信息的完整性;
* e) 可采用密码技术对从外部连接到内部网络的设备进行接入认证, 确保接入的设备身份真实性。

### 常用应对方案

* 网络边界访问控制大多由防火墙、路由器等设备实现, 实现d)项要求需要对相关设备进行改造, 调用**服务器密码机**进行完整性校验, **改造与实现成本相对较高**, 考虑到此项分数权重低, 一般不做相关改造;
* 从外部连接到内部网络的设备范围难以控制和界定, 对于这些设备的身份鉴别采用密码技术在落地环节是相当困难的, 且e)项为“**可**”的指标, **因此一般方案中可以引导定义为“不适用”项**。

---

### 网络和通信安全层面小结:

> 网络和通信安全层面根据业务具体场景, 通过选择部署**SSL站点证书、国密浏览器、SSL VPN安全网关、USBkey**等商密产品, 一般可以拿到**12-16分**, 属于相对好拿分的层面, 属于密评项目中的改造重点。
> ==End of OCR for page 19==

==Start of OCR for page 20==
**Header:** 密评要求详解——技术要求(设备和计算安全) | 安恒信息 | 构建安全可信的数字世界

以常用的密评三级要求为例, 设备和计算安全层面共计10分, 包含8项技术要求, 其中2项为产品资质要求。通常情况下应用系统涉及到的设备种类众多, 在正式分析技术要求前需要对设备和计算安全层面的测评对象进行梳理, 主要包括以下几种:

| 测评对象类型                             | 常见设备举例                                                                | 测评粒度                                                                                     |
| :--------------------------------------- | :-------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------- |
| **通用设备**                       | 应用服务器、数据库服务器、数据库管理系统等                                  | 有相同硬件配置和软件配置的设备作为一个测评对象, 如生产厂商、型号、操作系统版本、中间件等相同 |
| **具有密码功能的网络及安全设备**   | 堡垒机                                                                      |                                                                                              |
| **密码产品/设备**                  | SSL VPN安全网关、安全认证网关、服务器密码机、签名验签服务器、密钥管理系统等 | 有同一商用密码产品认证证书的密码产品作为一个测评对象                                         |
| **不具备密码功能的网络及安全设备** | 交换机、网闸、防火墙、WAF等                                                 | 一般不作为设备和计算层面的测评对象                                                           |
| ==End of OCR for page 20==               |                                                                             |                                                                                              |

==Start of OCR for page 21==
**Header:** 密评要求详解——技术要求(设备和计算安全) | 安恒信息 | 构建安全可信的数字世界

### 技术要求

* a) 应采用密码技术对登录设备的用户进行身份鉴别, 保证用户身份的真实性 **(高风险项)**

### 常用应对方案

设备层面基于密码技术实现身份鉴别的方式主要包括智能密码钥匙、动态令牌等。但实际场景下, 服务器、数据库等通用设备的改造难度大, 成本高, 因此通常采用以下方案进行响应:

* **密码设备**采用**UKEY**进行身份鉴别;
* 采用**国密堡垒机**时, 堡垒机使用**UKEY**进行身份鉴别, 通过堡垒机进行统一管理其它通用设备, **可以缓解**被运维设备的身份鉴别风险;
* 采用**非国密堡垒机**时, 需要使用运维专用的**国密SSL VPN网关**, 且运维人员使用**UKEY**进行身份鉴别, 并无法绕过VPN进行远程运维管理, **可以缓解**堡垒机及其被运维设备的身份鉴别风险。

#### 架构图对比

* **国密堡垒机场景:** 运维终端 (USB-Key) -> 国密堡垒机 -> (应用服务器 / 数据库 / 服务器密码机 / 签名验签服务器)
* **非国密堡垒机场景:** 运维终端 (USB-Key) -> 国密SSL VPN网关 (运维专用) -> 非国密堡垒机 -> (应用服务器 / 数据库 / 服务器密码机 / 签名验签服务器)
  ==End of OCR for page 21==

==Start of OCR for page 22==
**Header:** 密评要求详解——技术要求(设备和计算安全) | 安恒信息 | 构建安全可信的数字世界

### 技术要求

* b) 远程管理设备时, 应采用密码技术建立安全的信息传输通道 **(高风险项)**

### 常用应对方案

此处所提到的远程管理通道需注意和网络和传输层面的运维通道进行区分, 以运维管理员通过VPN登录到堡垒机再登录服务器进行运维为例, **仅堡垒机到服务器的通道是服务器远程管理通道的测评对象**。

* 采用**国密堡垒机**时, 访问堡垒机采用的是**GMSSL协议**, **得全部分**; 采用**非国密堡垒机**时, 访问堡垒机采用的是**HTTPS协议**, **部分得分**;
* 通过堡垒机运维服务器/数据库采用**SSH协议** (注意要摒弃SSH1.0等不安全协议), **部分得分**; 对于关闭远程运维只支持本地运维的数据库, 此项不适用。

#### 架构图

* **国密堡垒机场景:** 运维终端(USB-Key) -> (国密SSL VPN隧道) -> 国密SSL VPN网关 -> (GMSSL) -> 国密堡垒机 -> (SSH) -> 应用服务器/数据库
* **非国密堡垒机场景:** 运维终端(USB-Key) -> (国密SSL VPN隧道) -> 国密SSL VPN网关 -> (HTTPS) -> 非国密堡垒机 -> (SSH) -> 应用服务器/数据库
  ==End of OCR for page 22==

==Start of OCR for page 23==
**Header:** 密评要求详解——技术要求(设备和计算安全) | 安恒信息 | 构建安全可信的数字世界

### 技术要求

* c) 宜采用密码技术保证系统资源访问控制信息的完整性;
* d) 宜采用密码技术保证设备中的重要信息资源安全标记的完整性;
* e) 宜采用密码技术保证日志记录的完整性;
* f) 宜采用密码技术对重要可执行程序进行完整性保护, 并对其来源进行真实性验证;

### 常用应对方案

* 实现c)项需要相关设备调用**服务器密码机**进行访问控制信息的完整性校验, **改造与实现成本相对较高**, 考虑到此项分数权重低, 一般不做相关改造;
* 通常测评的设备及系统不涉及重要信息资源安全标记, 因此d)项会处理为“**不适用**”;
* 实现e)项需要相关设备调用**服务器密码机**对日志进行完整性校验, 改造成本较高, 也可以选择把日志记录**统一发送到加密存储设备(如日志审计)**进行完整性保护;
* 实现f)项需要相关设备调用**服务器密码机**对完整性、采用**CA证书**等方式对真实性进行验证, **改造与实现成本相对较高**, 一般不做相关改造;
* 以上主要针对通用设备, 对于通过了商用密码产品认证的密码设备, c)项、e)项、f)项均可全部得分。

---

### 设备和计算安全层面小结:

> 设备和计算安全层面涉及到服务器、数据库等多类设备, 导致其拿分难度较大且分数有限。因此实际项目中侧重于降高风险、拿基础分, 通常采用**国密SSL VPN、国密堡垒机、USBkey**等产品, 一般可以拿到**4-6分**, 相比于分数更重要的是缓解高风险项。
> ==End of OCR for page 23==

==Start of OCR for page 24==
**Header:** 密评要求详解——技术要求(应用和数据安全) | 安恒信息 | 构建安全可信的数字世界

以常用的密评三级要求为例, 应用和数据安全层面共计30分, 包含10技术要求, 其中2项为产品资质要求。

### 技术要求

* a) 应采用密码技术对登录用户进行身份鉴别, 保证应用系统用户身份的真实性 **(高风险项)**

### 常用应对方案

此处的用户指登录到应用系统的用户, 常见方式包括使用数字证书、动态令牌等, 具体场景又可细分为PC端身份鉴别和移动端身份鉴别。

#### 一、PC端身份鉴别

* **方式一: 采用数字证书 + 安全认证网关**

  * 安全认证网关需要与应用系统账号体系进行对接。
  * **架构:** 用户终端(USB-Key) -> (Internet) -> 安全认证网关 (与应用系统账号对接) -> 应用系统。由CA机构颁发证书。
* **方式二: 采用数字证书 + 签名验签服务器**

  * 应用系统需要与签名验签服务器进行接口对接。
  * **架构:** 用户终端(USB-Key) -> (Internet) -> 签名验签服务器 (与应用系统接口对接) -> 应用系统。由CA机构颁发证书。
* **方式三: 采用动态口令系统**

  * 应用系统需要与动态口令系统进行接口对接。
  * **架构:** 用户终端(动态令牌) -> (Internet) -> 动态口令服务器 (与应用系统接口对接) -> 应用系统。
    ==End of OCR for page 24==

==Start of OCR for page 25==
**Header:** 密评要求详解——技术要求(应用和数据安全) | 安恒信息 | 构建安全可信的数字世界

#### 二、移动端身份鉴别

* 采用**协同签名系统**, 移动终端需要集成**协同签名SDK模块**, 应用系统需要与协同签名系统进行接口对接。
  * **架构:** 移动终端(集成协同签名SDK) -> (Internet) -> 协同签名系统 (与应用系统接口对接) -> 应用系统。由CA机构颁发证书。

### 技术要求

* b) 宜采用密码技术保证信息系统应用的访问控制信息的完整性;
* c) 宜采用密码技术保证信息系统应用的重要信息资源安全标记的完整性;

### 常用应对方案

* b)项可通过应用系统对接**服务器密码机**使用SM3-HMAC或对接**签名验签服务器**使用数字证书方式进行完整性校验, 使用密码机的方式更常见;
* 由于应用系统重要资源的分类和重要数据的分类通常是有一定重复的, 例如: 用户信息, 交易记录、财务数据等。一般测评时为避免重复测评, c)项通常处理为**不适用**。

### 技术要求

* d) 应采用密码技术保证信息系统应用的重要数据在传输过程中的机密性; **(高风险项)**
* f) 宜采用密码技术保证信息系统应用的重要数据在传输过程中的完整性;

### 常用应对方案

上述两项对于重要数据在传输过程中机密性和完整性的要求, 通常可选择以下两种方式应对:

* 通过**网络和通信安全层面**的合规通信信道方式进行高风险缓解, 即通信实体间采用符合要求的密码技术建立网络通信信道, 且网络通信信道经评估无高风险, 如国密VPN隧道、国密HTTPS等;
* 采用**传输加密类产品**对传输过程中的重要数据进行机密性和完整性保护。
  ==End of OCR for page 25==

==Start of OCR for page 26==
**Header:** 密评要求详解——技术要求(应用和数据安全) | 安恒信息 | 构建安全可信的数字世界

### 技术要求

* e) 宜采用密码技术保证信息系统应用的重要数据在存储过程中的机密性 **(高风险无缓解措施)**
* g) 宜采用密码技术保证信息系统应用的重要数据在存储过程中的完整性 **(高风险项)**

### 常用应对方案

通常采用**服务器密码机**对存储的重要数据进行机密性和完整性保护, 其中完整性保护也可以通过**签名验签服务器**实现。

#### 数据加解密流程图 (服务器密码机方案)

* **写数据过程**

  1. 用户发起**写数据**请求。
  2. 应用服务器(集成SDK)判断数据是否需要加密, 若需要则调用密码机SDK。
  3. 应用服务器将**数据明文**发送到**服务器密码机**进行加密。
  4. 服务器密码机加密后返回**密文+HMAC**。
  5. 应用服务器将**密文+HMAC**落盘存储到**数据库**。
  6. (返回成功)
* **读数据过程**

  1. 用户发起**读数据请求**。
  2. 应用服务器从数据库读取**密文+HMAC**并发送至应用服务器。
  3. 应用服务器调用密码机SDK, 将**密文**发至**服务器密码机**进行解密, 同时**HMAC**保存待比对。
  4. 服务器密码机解密后返回**明文+HMAC'**。
  5. 应用服务器SDK比对前后两次的HMAC是否一致, 校验通过则向用户返回数据。
  6. **成功读取数据**。
     ==End of OCR for page 26==

==Start of OCR for page 27==
**Header:** 密评要求详解——技术要求(应用和数据安全) | 安恒信息 | 构建安全可信的数字世界

### 技术要求

* h) 在可能涉及法律责任认定的应用中, 宜采用密码技术提供数据原发证据和数据接收证据, 实现数据原发行为的不可否认性和数据接收行为的不可否认性 **(高风险无缓解措施)**

### 常用应对方案

h)项仅对“**可能涉及法律责任认定的应用**”适用, 如金融电子交易系统、电子公文系统、医疗保险理赔系统等。通常采用**数字签名**技术保证不可否认性, 如涉及印章的应用可采用**电子签章系统**、对时间敏感的业务可采用**时间戳服务器**等。

* **架构图:**
  * 用户终端(USB-Key) -> (Internet) -> [应用系统, 签名验签服务器, 时间戳服务器, 电子签章系统] -> CA机构

---

### 应用和数据安全层面小结:

> 应用和数据安全层面是技术要求中分数占比最高的部分, 其业务改造成本和难度也最大。通过部署**安全认证网关、服务器密码机**等产品并完成应用系统集成后能够拿到**25分**以上。各应用系统之间差异大难以一概而论, 项目中需要结合实际情况具体分析。
> ==End of OCR for page 27==

==Start of OCR for page 28==
**Header:** 密评要求详解——管理要求 | 安恒信息 | 构建安全可信的数字世界

以常用的密评三级要求为例, 管理要求共计四个层面19个要求项, 其中包括2个高风险项, 项目上一般通过文档资料的补充, 通常可以拿到25分左右。

*(这是一个思维导图，以下用嵌套列表进行结构化转译)*

* **39786: 三级信息系统安全管理制度**
  * **管理制度 (8分)**
    * 应具备密码应用安全管理制度
    * 应建立相应密钥管理制度
    * 应建立操作规程
    * 应定期修改安全管理制度
    * 应明确管理制度发布流程
    * 应制定相关执行记录并妥善保存
  * **人员管理 (8分)**
    * 应了解并遵守密码相关法律法规、密码应用安全管理制度
    * 应建立密码应用岗位责任制度
    * 应建立上岗人员培训制度
    * 应定期对密码应用安全岗位人员进行考核
    * 应建立关键人员保密制度和调离制度
  * **建设运行 (8分)**
    * 应依据密码相关标准和密码应用需求, **制定密码应用方案** **(黄色字体: 高风险项)** -> *(如被测系统通过测评发现不存在高风险安全问题, 可酌情降低风险等级)*
    * 制定密钥安全管理策略
    * 制定实施方案
    * 投入运行前应进行密码应用安全性评估
    * 应定期开展密码应用安全性评估及攻防对抗演习
  * **应急处置 (6分)**
    * 应制定密码应用应急策略
    * 事件处置
    * 应向有关部门上报处置情况

**说明:**

* **黄色字体为高风险项**
* **深红色字体为高风险项且无缓解措施**
  ==End of OCR for page 28==

==Start of OCR for page 29==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

## 联系我们 (Contact Us)

浙江省杭州市滨江区西兴街道联慧街188号安恒大厦
www.dbappsecurity.com.cn
400-6059-110

*(Image: QR Code for "安恒信息官方微信")*
安恒信息官方微信
==End of OCR for page 29==
