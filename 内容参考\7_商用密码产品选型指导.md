# 商用密码培训课程——商用密码产品选型指导

**备注:** 基于产品性能进行选型请结合《商用密码产品性能指标及选型工具》表格搭配使用。

==Start of OCR for page 1==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

# 商用密码培训课程

## ——商用密码产品选型指导

**商密产品线**
www.dbappsecurity.com.cn 400-6059-110

*(Image: A modern office building, 安恒大厦)*
==End of OCR for page 1==

==Start of OCR for page 2==
**Header:** 安恒信息 | 构建安全可信的数字世界 (DAS-Security | Build A Secure And Credible Digital World)

# 目录 (Contents)

* **01** 商用密码产品品类概述
* **02** 存储加密类产品选型
* **03** 传输加密类产品选型
* **04** 身份认证类产品选型
  ==End of OCR for page 2==

==Start of OCR for page 3==

# 01 | 商用密码产品品类概述

==End of OCR for page 3==

==Start of OCR for page 4==
**Header:** 商用密码产品品类概述 | 安恒信息 | 构建安全可信的数字世界

商用密码全系列产品涉及传输加密类、存储加密类、密钥管理类等不同类型的产品。不同类型的密码产品选型差异较大:

1. **存储加密类**产品核心参数是**业务请求单次数据量**。
2. **密钥管理类**产品核心参数是**密钥存储空间**和**密钥并发量**。
3. **传输加密类**产品核心参数是**用户请求并发数**和**数据吞吐量**。
4. **身份认证类**产品核心参数是**用户请求并发量**。

### 产品图谱

#### 密码服务与监管平台

* **密码服务平台:**
  * **密码管理:** 密码服务管理、租户管理、应用管理、监控告警、密码态势、日志管理
  * **密码服务:** 安全通道服务、安全认证服务、数据加解密服务、签名验签服务、协同签名服务、密钥管理服务
* **密码应用监管平台:**
  * **全息档案:** 密码资产管理、密钥设备管理、密码厂商管理
  * **密评管理:** 密评总览、密评档案、检查任务管理
  * **综合态势:** 门户大屏、监管大屏、指挥调度
  * **其它:** 实时监测、教学培训、考核评价、行政执法

#### 密码整机类产品

* **传输加密类:**
  * SSL VPN安全网关 (传输机密性/完整性, 实体身份鉴别)
  * IPsec VPN网关 (传输机密性/完整性, 实体身份鉴别)
  * IPsec/SSL VPN综合安全网关 (IPsec/SSL融合, 传输机密性/完整性保护)
* **身份认证类:**
  * 安全认证网关 (用户身份鉴别, 传输机密性/完整性保护)
  * 签名验签服务器 (签名验签, 身份认证, 完整性校验)
  * 签名验签时间戳二合一服务器 (签名验签, 时间戳签发, 不可否认性)
* **存储加密类:**
  * 服务器密码机 (数据加解密, 完整性校验, 密钥管理)
  * 云服务器密码机 (数据加解密, 完整性校验, 支持虚拟化)

#### 密码系统类产品

* 密钥管理系统 (密钥生成、分发、更新)
* 协同签名系统 (无介质依赖, 密钥分割, 身份认证)
* 云密盾加密系统 (密钥管理, 数据加解密, 物联安全)
* 安恒密盾 (通讯加密, 无感易用, 专业运营)

#### 密评工具箱

* **测评工具:** 随机数检测工具, 通信协议检测工具, 密码算法检测工具, 数字证书检测工具
* **测评辅助:** 测评标准辅助, 测评流程辅助
  ==End of OCR for page 4==

==Start of OCR for page 5==

# 02 | 存储加密类产品选型

==End of OCR for page 5==

==Start of OCR for page 6==

## 服务器密码机产品选型

服务器密码机属于接口调用类产品, 能够提供数据加解密、完整性校验、真随机数生成、密钥生成、密钥管理等服务, 确保数据的机密性、完整性。

### 选型关键点

* **应用集成:** 应用系统需要集成SDK或调用API接口, 实现重要数据的加解密和完整性校验。
* **核心性能参数:** **SM3/SM4加解密速率 (Mbps)**。
* **性能影响:** 加解密的过程会影响应用系统的响应时间, 同时影响数据关联运算、模糊查询。

### 场景示意图

1. **业务人员**在应用数据中定义**敏感字段** (如身份证号)。
2. **应用服务器 (集成SDK/API)** 调用密码机加密服务。
3. **服务器密码机**对明文进行加密，返回**密文**和**HMAC值**。
4. 密文和HMAC值落盘到**数据库**。

* **示例:** `select * from t where a='abc'` (明文查询) -> `select * from t where a='#¥%&'` (密文查询)
  ==End of OCR for page 6==

==Start of OCR for page 7==

### 敏感字段加密及完整性保护场景

**假设:** 政务办公场景中公务员系统通过API方式实现敏感字段的加解密, 在业务高峰期同时并发**1000个用户**登录系统并完成业务操作, 单个用户每次登录操作产生**10个敏感字段**, 每个敏感字段平均**300Byte**大小。

**场景建议及解读:**

1. 敏感字段加密和完整性保护建议使用**HTTP服务接口**, 用户开发工作量较小。
2. 密码机加密速率压测性能: **550Mbps** (线程数32、CPU90%)
3. 密码机HMAC速率压测性能: **900Mbps** (线程数96、CPU86%)

**计算公式:** `TPS = XX Mbps * 1024 * 1024 / 8 / 字节数`

**性能测算表:**

| 设备型号       | 应用系统 | 系统加密请求并发数(次) | 敏感字段数(个) | 敏感字段平均大小(Byte) | 单台加密速率(Mbps) | 密码业务的冗余处理能力         | 集群通信性能损失 | 测算数量        |
| :------------- | :------- | :--------------------- | :------------- | :--------------------- | :----------------- | :----------------------------- | :--------------- | :-------------- |
| FLK-XC-CDE-SPM | A系统    | 10000                  | 10             | 300                    | 550                | 15%                            | 5%               | 0.490           |
|                | B系统    | 2000                   | 20             | 200                    | 200                | 15%                            | 5%               | 0.359           |
|                | C系统    | 1000                   | 20             | 150                    | 200                | 15%                            | 5%               | 0.135           |
|                |          |                        |                |                        |                    | **小计**                 |                  | **0.983** |
|                |          |                        |                |                        |                    | **设备数量(向上取整)**   |                  | **1**     |
|                |          |                        |                |                        |                    | **高可用、冗余需求数量** |                  | **0**     |
|                |          |                        |                |                        |                    | **总计**                 |                  | **1**     |

**判定逻辑:**

1. **判断请求并发是否超过设备性能:**
   * 如果 `(业务并发数 / 单台设备TPS) / (1 - 冗余能力) > 1`，则需要计算集群性能损失。
2. **计算设备数量:**
   * **情况一 (需要集群):** `(业务并发数 / 单台设备TPS) / (1 - 冗余能力) / (1 - 集群损失)`
   * **情况二 (单台足够):** `(业务并发数 / 单台设备TPS) / (1 - 冗余能力)`
     ==End of OCR for page 7==

==Start of OCR for page 8==

### 扩展知识: 常见敏感字段及字节大小估算

| 序号 | 敏感数据     | 描述                                   | 示例                                  | UTF-8编码计算字节Byte数 |
| :--- | :----------- | :------------------------------------- | :------------------------------------ | :---------------------- |
| 1    | 用户登录口令 | 用户重要鉴别信息                       | JIA123mi                              | 8                       |
| 2    | 身份证号     | 真实身份鉴别信息                       | 370191202300006541                    | 18                      |
| 3    | 手机号       | 手机号码                               | 18516001234                           | 11                      |
| 4    | 姓名         | 真实姓名                               | 诸葛孔明                              | 12                      |
| 5    | 邮箱         | 邮箱地址                               | <EMAIL>                   | 19                      |
| 6    | 操作日志数据 | 用户名、IP、操作类型、时间、方法、内容 | 张三 ************* 新增 2023/09/12... | 70-80                   |
| 7    | 业务数据     | 订单信息(订单号、用户名、类型、费用等) | (1K=1024B) 10K                        | 10240                   |

**编码说明:**

1. **UTF-8:** 1个汉字(含中文符号) = 3字节, 1个英文(含英文符号)/数字 = 1字节。
2. **GBK/GB2312:** 1个汉字(含中文符号) = 2字节, 1个英文(含英文符号)/数字 = 1字节。
3. **Unicode:** 1个汉字/英文/数字 = 4字节。
4. **ASCII:** 1个英文(含英文符号)/数字 = 1字节 (不支持中文)。
5. **ISO 8859-1:** 1个英文(含英文符号)/数字 = 1字节 (不支持中文)。
   ==End of OCR for page 8==

==Start of OCR for page 9==

## 云服务器密码机产品选型

云服务器密码机主要解决云场景中云平台和租户密评时, **密码资源弹性扩容、降低密码硬件设备成本**的问题。云服务器密码机利用虚拟化技术将一台物理密码机虚拟为多台虚拟密码机, 每台虚拟机对应用独立提供密钥管理和密码运算等服务。

### 核心特性

* **隔离性:**
  * **管理隔离:** VSM(虚拟密码机)的管理IP、端口不同; 宿主机与VSM不共享用户信息。
  * **使用隔离:** VSM的服务IP、域名或端口不同。
  * **系统隔离:** VSM的系统、CPU、内存、存储等独立运行, 相互隔离。
  * **网络隔离:** SR-IOV网络虚拟化, VSM独立虚拟网络接口, VLAN隔离。
* **云化部署:** 支持**RESTful接口**与云管理平台对接, 同时提供标准接口为云上业务系统提供密码服务。
* **密钥存储:** 密码卡中Flash空间划分多个, VSM独享Flash空间, 虚拟密码卡使用管理密钥对卡内信息加密。
* **密钥使用:** 密码服务进程分别运行在各自虚拟机, 密码服务使用密钥时, 虚拟机独享虚拟机密码卡完成运算。
* **功能原理:** 虚拟密码机与服务器密码机功能和产品原理类似。

*(注：性能图显示，随着VSM数量的增加，性能会线性叠加，但由于虚拟化开销(VSM开销)，总性能会略低于物理机的理论性能上限。)*
==End of OCR for page 9==

==Start of OCR for page 10==

### 敏感字段加密和完整性保护场景 (云环境)

**假设:** 云场景中某租户业务系统通过集成SDK方式实现敏感字段的加解密, 在业务高峰期同时并发**1000个用户**登录系统并完成业务操作, 单个用户每次登录操作产生**10个敏感字段**, 每个敏感字段平均**300Byte**大小。

**场景建议及解读:**

1. 敏感字段加密和完整性保护建议使用**HTTP服务接口**, 用户开发工作量较小。
2. 单VSM加密速率压测性能: **30 Mbps** (线程数3、虚拟机CPU90%、宿主机2.7%)
3. 单VSM HMAC速率压测性能: **35Mbps** (线程数2、虚拟机CPU94.6%、宿主机3%)
4. 32台VSM并发加密速率压测性能: **500 Mbps**
5. 32台VSM HMAC速率压测性能: **600Mbps**

**性能测算表 (测算所需VSM数量):**

| 应用系统                   | 系统加密请求并发数(次) | 敏感字段数(个) | 敏感字段平均大小(Byte) | VSM加密速率(Mbps) | 密码业务的冗余处理能力         | 集群通信性能损失 | 测算数量        |
| :------------------------- | :--------------------- | :------------- | :--------------------- | :---------------- | :----------------------------- | :--------------- | :-------------- |
| A系统                      | 1000                   | 10             | 300                    | 30                | 15%                            | 5%               | 0.898           |
| B系统                      | 2000                   | 20             | 150                    | 30                | 15%                            | 5%               | 1.890           |
| C系统                      | 100                    | 5              | 3000                   | 30                | 15%                            | 5%               | 0.449           |
|                            |                        |                |                        |                   | **小计**                 |                  | **3.236** |
|                            |                        |                |                        |                   | **虚拟机数量(向上取整)** |                  | **4**     |
|                            |                        |                |                        |                   | **高可用、冗余需求数量** |                  | **1**     |
|                            |                        |                |                        |                   | **总计**                 |                  | **5**     |
| ==End of OCR for page 10== |                        |                |                        |                   |                                |                  |                 |

==Start of OCR for page 11==

# 03 | 传输加密类产品选型

==End of OCR for page 11==

==Start of OCR for page 12==

## SSL VPN安全网关产品选型

SSL VPN安全网关基于GMSSL开发, 可在OSI模型的二、三层上建立隧道, 使用SSL/TLS协议协商加密算法与加密密钥, 应用虚拟网卡TUN/TAP驱动来扩展网络。

### 产品组成与关键技术指标

* SSL VPN网关为软硬一体式设备, VPN模式下需要在客户端/服务端安装客户端软件, 可基于虚拟网卡实现安全的VPN隧道。
* 网关工作过程主要是基于SSL实现通信双方的握手(单/双向)。

### 选型计算场景

**测算逻辑:** SSL VPN的性能瓶颈主要在于**并发用户数**和**加密吞吐量**。选型时需要同时满足这两个指标的要求，取两者计算出的较大值作为设备需求基数。

**性能测算表:**

| 设备型号          | 应用系统 | 系统最大并发用户数(个) | 单设备最大并发用户数(个) | 系统最大流量(Mbps) | 单设备加密吞吐量(Mbps) | 密码业务的冗余处理能力              | 集群通信性能损失 | 测算数量        |
| :---------------- | :------- | :--------------------- | :----------------------- | :----------------- | :--------------------- | :---------------------------------- | :--------------- | :-------------- |
| FLK-SSLSG-G600-HU | A系统    | 100                    | 8000                     | 300                | 2500                   | 20%                                 | 5%               | 0.150           |
|                   | B系统    | 500                    | 30000                    | 1000               | 2500                   | 20%                                 | 5%               | 0.500           |
|                   |          |                        |                          |                    |                        | **小计**                      |                  | **0.650** |
|                   |          |                        |                          |                    |                        | **SSL VPN基本要求(向上取整)** |                  | **1**     |
|                   |          |                        |                          |                    |                        | **高可用、冗余需求数量**      |                  | **1**     |
|                   |          |                        |                          |                    |                        | **总计(SSL VPN需求总数)**     |                  | **2**     |

*(注: 测算数量 = MAX(系统并发/设备并发, 系统流量/设备吞吐) 基础上再考虑冗余和集群损耗)*
==End of OCR for page 12==

==Start of OCR for page 13==

## IPSec VPN网关产品选型

IPSec VPN网关系统是为防止数据在传输过程中被第三方获取或篡改的安全产品, 提供安全隧道的协商建立与维护、数据的加解密。

### 产品组成与关键技术指标

* IPSec VPN网关为软硬一体式设备, VPN模式下需要在客户端/服务端安装客户端软件, 可基于虚拟网卡实现安全的VPN隧道。
* 网关工作过程主要是基于IPsec建立安全隧道。
* 在密评中, IPSec VPN安全网关主要用来响应**通信实体身份鉴别**、**通信过程中数据的完整性**、**通信过程中重要数据的机- 密性保护**(一般是灾备通道)。
* **核心性能指标:** 吞吐率 (e.g., 500Mbps)

**性能测算表:**

| 系统最大流量(Mbps)         | 设备吞吐量(Mbps) | 密码业务的冗余处理能力 | 测算数量        |
| :------------------------- | :--------------- | :--------------------- | :-------------- |
| 200                        | 500              | 10%                    | 0.444           |
|                            |                  | **小计**         | **0.444** |
|                            |                  | **向上取整**     | **1**     |
|                            |                  | **总计**         | **1**     |
| ==End of OCR for page 13== |                  |                        |                 |

==Start of OCR for page 14==

## 云密盾加密系统&协同签名系统产品选型

云密盾加密系统是面向C/S架构系统, 提供基于国密算法数据加密能力的集**身份认证、密钥管理、加解密能力**实现的模块化系统。云密盾系统本身只提供设备身份认证(协同签名)、密钥管理(数据加密密钥协商)能力, 具体的数据加解密能力实现依赖**SDK密码模块**。

### 产品组成与关键技术指标

* **云密盾加密系统核心指标有两个: 协同签名并发数、SDK加解密速率**
  1. **协同签名并发数:** 性能瓶颈在于协同签名及密钥下发并发能力(如1000次), 通过增加服务端设备会提升性能。
  2. **SDK加解密速率:** 性能瓶颈依赖于前端硬件CPU和内存资源, 增加CPU线程会提升性能。**测试数据: 单线程30Mbps (CPU: 2.5 GHz)**

**性能测算表:**

| 应用系统                   | 系统最大并发用户数(次) | 最大并发调用数 | 冗余处理能力                                 | 集群通信性能损失 | 测算数量        |
| :------------------------- | :--------------------- | :------------- | :------------------------------------------- | :--------------- | :-------------- |
| A系统                      | 500                    | 1000           | 10%                                          | 5%               | 0.556           |
|                            |                        |                | **小计**                               |                  | **0.556** |
|                            |                        |                | **云密盾和协同签名基本要求(向上取整)** |                  | **1**     |
|                            |                        |                | **高可用、冗余需求数量**               |                  | **0**     |
|                            |                        |                | **总计(云密盾和协同签名需求总数)**     |                  | **1**     |
| ==End of OCR for page 14== |                        |                |                                              |                  |                 |

==Start of OCR for page 15==

# 04 | 身份认证类产品选型

==End of OCR for page 15==

==Start of OCR for page 16==

## 安全认证网关产品选型

安全认证网关是采用数字证书为应用系统提供用户管理、身份鉴别、单点登录、传输加密、访问控制和安全审计服务的产品。基于国密SSL站点证书对应用系统实现反向代理(https), 亦可对接应用系统用户认证体系(结合用户侧的证书认证)实现双向身份鉴别(网络和通信、应用和数据层面)。

### 产品组成与关键技术指标

* **核心性能指标:** **最大并发连接数(个)** 和 **设备并发吞吐量(Mbps)**。
* **选型逻辑:** 与SSL VPN类似，需要同时满足并发连接数和流量吞吐两个维度的性能要求。

**性能测算表:**

| 应用系统                   | 并发连接数 | 最大并发连接数(个) | 系统流量(Mbps) | 设备并发吞吐量(Mbps) | 密码业务的冗余处理能力                   | 集群通信性能损失 | 设备测算数量    |
| :------------------------- | :--------- | :----------------- | :------------- | :------------------- | :--------------------------------------- | :--------------- | :-------------- |
| A系统                      | 20000      | 50000              | 500            | 1400                 | 20%                                      | 5%               | 0.500           |
| B系统                      | 10000      | 50000              | 200            | 1400                 | 20%                                      | 5%               | 0.250           |
|                            |            |                    |                |                      | **小计**                           |                  | **0.750** |
|                            |            |                    |                |                      | **安全认证网关基本要求(向上取整)** |                  | **1**     |
|                            |            |                    |                |                      | **高可用、冗余需求数量**           |                  | **1**     |
|                            |            |                    |                |                      | **总计(SSL VPN需求总数)**          |                  | **2**     |
| ==End of OCR for page 16== |            |                    |                |                      |                                          |                  |                 |

==Start of OCR for page 17==

## 签名验签服务器产品选型

签名验签服务器主要为各类信息系统、终端用户提供基于PKI体系和数字证书的数字签名、验证签名等服务, 保证关键业务信息的真实性、完整性和不可否认性。

### 产品组成与关键技术指标

* **核心性能指标:**
  * **用户认证场景:** **单设备应用最大并发调用数(个)**, 例如 33000次/秒。
  * **系统之间身份鉴别:** **实际业务交互并发数(应用系统定义)**, 按项目评估。

**性能测算表:**

| 应用系统                   | 终端登录并发数(个) | 单设备应用最大并发调用数(个) | 密码业务的冗余处理能力                   | 集群通信性能损失 | 测算数量        |
| :------------------------- | :----------------- | :--------------------------- | :--------------------------------------- | :--------------- | :-------------- |
| A系统                      | 2000               | 33000                        | 20%                                      | 5%               | 0.076           |
| B系统                      | 2000               | 33000                        | 20%                                      | 5%               | 0.076           |
|                            |                    |                              | **小计**                           |                  | **0.152** |
|                            |                    |                              | **服务器密码机基本要求(向上取整)** |                  | **1**     |
|                            |                    |                              | **高可用、冗余需求数量**           |                  | **0**     |
|                            |                    |                              | **总计(服务器密码机需求总数)**     |                  | **1**     |
| ==End of OCR for page 17== |                    |                              |                                          |                  |                 |

==Start of OCR for page 18==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

## 联系我们 (Contact Us)

浙江省杭州市滨江区西兴街道联慧街188号安恒大厦
www.dbappsecurity.com.cn
400-6059-110

*(Image: QR Code for "安恒信息官方微信")*
安恒信息官方微信
==End of OCR for page 18==
