# 商用密码培训课程——常见商用密码产品介绍

==Start of OCR for page 1==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

# 商用密码培训课程

## ——常见商用密码产品介绍

**商密产品线**
www.dbappsecurity.com.cn 400-6059-110

*(Image: A modern office building, 安恒大厦)*
==End of OCR for page 1==

==Start of OCR for page 2==
**Header:** 安恒信息 | 构建安全可信的数字世界 (DAS-Security | Build A Secure And Credible Digital World)

# 目录 (Contents)

* **01** 商用密码产品整体说明
* **02** 基础类商用密码产品介绍
* **03** 应用类商用密码产品介绍
* **04** 其它商用密码产品介绍
  ==End of OCR for page 2==

==Start of OCR for page 3==

# 01 | 商用密码产品整体说明

==End of OCR for page 3==

==Start of OCR for page 4==
**Header:** 商用密码产品的定义和分类 | 安恒信息 | 构建安全可信的数字世界

**商用密码产品**是指实现密码运算、密钥管理等密码相关功能的硬件、软件、固件或其组合。目前通过国家密码管理局审批的商用密码通用产品已超过3000款, 常见的分类方式包括:

| 分类方式                  | 类别                   | 定义及示例                                                                                   |
| :------------------------ | :--------------------- | :------------------------------------------------------------------------------------------- |
| **产品形态**        | **软件**         | 指以纯软件形态出现的密码产品, 如密码算法软件                                                 |
|                           | **芯片**         | 指以芯片形态出现的密码产品, 如算法芯片、安全芯片                                             |
|                           | **模块**         | 指将单一芯片或多芯片组装在同一块电路板上, 具备专用密码功能的产品, 如加解密模块、安全控制模块 |
|                           | **板卡**         | 指以板卡形态出现的密码产品, 如智能 IC 卡、智能密码钥匙、密码卡                               |
|                           | **整机**         | 指以整机形态出现的密码产品, 如SSL VPN网关、服务器密码机                                      |
|                           | **系统**         | 指以系统形态出现, 由密码功能支撑的产品, 如证书认证系统、密钥管理系统                         |
| **产品功能**        | **密码算法类**   | 指提供基础密码运算功能的产品, 如密码芯片等                                                   |
|                           | **数据加解密类** | 指提供数据加解密功能的产品, 如服务器密码机、云服务器密码机、VPN 设备等                       |
|                           | **认证鉴别类**   | 指提供身份鉴别等功能的产品, 如认证网关、动态口令系统、签名验签服务器等                       |
|                           | **证书管理类**   | 指提供证书产生、分发、管理功能的产品, 如数字证书、证书认证系统等                             |
|                           | **密钥管理类**   | 指提供密钥产生、分发、更新、归档和恢复等功能的产品, 如密钥管理系统等                         |
|                           | **密码防伪类**   | 指提供密码防伪验证功能的产品, 如电子印章系统、时间戳服务器等                                 |
|                           | **综合类**       | 指提供含上述六类产品功能的两种或两种以上的产品, 如自动柜员机(ATM)密码应用系统等              |
| ==End of OCR for page 4== |                        |                                                                                              |

==Start of OCR for page 5==
**Header:** 商用密码产品证书及相关检测规范 | 安恒信息 | 构建安全可信的数字世界

国家对商用密码产品实行**强制性检测认证制度**, 即产品需具备**《商用密码产品认证证书》**, 才能正常销售。国家密码管理局先后分两批次发布《商用密码产品认证目录》, 定义了**28类商用密码产品**的认证依据和技术规范, 非标准产品统一归类到“其他密码模块”中。

### 产品标准和技术要求

* 如**GM/T 0025《SSL VPN 网关产品规范》**、**GM/T 0030《服务器密码机技术规范》**等, 详细规定了对应产品的功能要求、安全性要求、检测要求等内容。

### 密码产品的安全等级

* 在**GM/T 0028《密码模块安全技术要求》**、**GB/T 37092-2018《信息安全技术 密码模块安全检测要求》**标准中, 规定了从安全一级到安全四级四个安全要求递增的安全等级:

| 级别               | 说明                                                                                                                           | 适用范围         |
| :----------------- | :----------------------------------------------------------------------------------------------------------------------------- | :--------------- |
| **安全一级** | 最低等级的安全要求                                                                                                             | 满足二级密评要求 |
| **安全二级** | 在安全一级的基础上增加了拆卸证据、基于角色的鉴别等功能要求, 可以抵抗使用简单工具的主动攻击                                     | 满足三级密评要求 |
| **安全三级** | 在安全二级的基础上, 增加了物理安全、身份鉴别、环境保护、非入侵式攻击缓解参数管理等安全机制, 可以抵抗使用简单工具的中等强度攻击 | 满足四级密评要求 |
| **安全四级** | 最高安全等级, 包括以上所有安全特性, 并增加了一些扩展特性, 可以抵抗使用特制工具的高强度长时间攻击                               | /                |

*(Image: Sample of a "商用密码产品认证证书")*
==End of OCR for page 5==

==Start of OCR for page 6==

# 02 | 基础类商用密码产品介绍

==End of OCR for page 6==

==Start of OCR for page 7==

## 1. 服务器密码机

**服务器密码机**是常见的密码设备之一, 能够为信息安全系统提供包括数据加解密、数字签名与验签、密钥管理、消息验证等密码服务, 满足业务交易对数据从产生、传输、处理、存储过程中的**机密性、完整性、不可抵赖性**以及**身份认证**的要求。

### 产品核心功能

服务器密码机通常为软硬一体形态, 采用接口调用的工作方式, 需要应用系统集成相关SDK, 调用接口实现数据加解密和完整性校验。服务器密码机的核心性能参数主要包括**密钥生成数**(如对称密钥2000对)、**算法性能数**(如SM4: 2Gbps)两类。在密评建设中, 服务器密码机主要用来响应**设备和计算层面完整性**、**应用和数据安全层面数据存储的机密性和完整性**的相关要求。

* **提供多种密码服务:** 应用通过调用服务器密码机接口, 密码机可提供包括数据加解密、完整性校验、数字签名等多种基础密码功能。
* **三级密钥管理体系:** 依托完善的三级密钥管理体系实现数据加密、数字签名、随机数生成等运算, 保障系统安全。
* **设备安全自检:** 具备对产生随机数的随机性、密码算法正确性检验的安全机制, 从而确保密码算法始终处于正确、安全的工作状态中。
* **日志审计机制:** 提供日志分级记录, 同时支持Syslog协议, 支持对接第三方日志审计设备, 实现统一的日志收集和管理等操作。
* **标准服务接口:** 遵循《信息安全技术密码设备应用接口规范》标准接口, 具备良好的通用性和适配性。
* **易操作的设备管理方式:** 提供B/S、C/S端管理工具, 对系统、设备、密钥、日志等进行管理, 保障系统安全、可信的运行。
  ==End of OCR for page 7==

==Start of OCR for page 8==

### 部署方式及工作原理

#### 写数据过程

1. **用户**发起写数据请求。
2. **应用服务器 (集成SDK)** 判断数据是否需要加密, 若需要则调用密码机SDK。
3. 应用服务器将**数据明文**发送至**服务器密码机**进行加密。
4. 服务器密码机加密后返回**密文+HMAC**给应用服务器。
5. 应用服务器将**密文+HMAC**落盘存储到**数据库**。

#### 读数据过程

1. **用户**发起读数据请求。
2. **应用服务器**从数据库中读取**密文+HMAC**。
3. 应用服务器调用密码机SDK, 将**密文**发至**服务器密码机**解密，并将**HMAC**保存用于后续比对。
4. 服务器密码机解密后返回**明文+新计算的HMAC'**。
5. 应用服务器SDK比对收到的HMAC和新计算的HMAC'是否一致, 校验通过则向用户返回解密后的数据。
6. **用户**成功读取数据。
   ==End of OCR for page 8==

==Start of OCR for page 9==

## 2. 云服务器密码机

**云服务器密码机**主要应用于云计算场景下, 利用虚拟化技术将一台物理密码机虚拟为多台虚拟密码机, 每台虚拟机对云上应用独立提供密码运算和密钥管理等服务, 满足云场景中密码资源弹性扩容、按需分配的需求。产品主要由**宿主机、虚拟机、管理工具** (宿主机管理端和虚拟机管理端) 构成。

### 虚拟化架构与核心功能

* **云化部署:** 云密码机部署在云环境中, 支持提供**RESTful接口**实现与云管理平台对接, 同时提供标准接口为云上业务系统提供密码服务。
* **隔离机制:**
  * **管理隔离:** VSM(虚拟密码机)的管理IP、管理域、管理端口不同；宿主机与VSM不共享用户信息。
  * **使用隔离:** VSM的服务IP 地址、服务域名或服务端口不同。
  * **系统隔离:** VSM系统、CPU、内存、存储空间等独立运行、相互隔离。
  * **网络隔离:** 采用SR-IOV网络虚拟化、VSM独立虚拟网络接口, Vlan隔离。
* **密钥存储:** 密码卡中Flash空间划分多个, VSM独享Flash空间, 虚拟密码卡使用管理密钥对卡内信息加密。
* **密钥使用:** 密码服务进程分别运行在各自虚拟机, 密码服务使用密钥时, 虚拟机独享虚拟机密码卡完成运算。
* **功能原理:** 虚拟密码机与服务器密码机功能和产品原理类似。

*(注：架构图展示了一台物理设备通过计算、网络、存储虚拟化技术，生成多个独立的虚拟密码机实例(VSM)，每个VSM为不同的租户应用(APP)提供隔离的密码服务。)*
==End of OCR for page 9==

==Start of OCR for page 10==

## 3. 签名验签服务器

**签名验签服务器**是基于数字证书和PKI体系, 为业务系统提供数字签名及验证、数字信封制作及解析、证书管理等功能, 结合安全、完善的密钥管理机制, 保障业务数据从产生、传输、接收到处理整个过程的**机密性、有效性、完整性和不可抵赖性**。

### 产品核心功能

签名验签服务器通常为软硬一体形态, 采用接口调用的工作方式, 需要应用服务端集成相关SDK, 调用接口实现签名/验签功能。因此, 签名验签服务器的核心性能参数即**SM2签名/验签性能**(如20000次/秒)。在密评建设中, 签名验签服务器主要用来响应**应用和数据安全层面**对**用户身份鉴别、存储数据完整性、不可否认性**的相关要求。

* **应用管理:** 提供包括应用实体注册、配置密钥、关联证书等应用管理功能。
* **证书管理:**
  * 支持对CA根证书的导入、导出、删除。
  * 支持应用证书、用户证书的申请、导入、删除、备份。
  * 支持OCSP、CRL、OCSP+CRL的多种证书验证策略。
* **密钥管理:** 采用三级密钥管理机制, 支持密钥的产生、存储、导入、导出、使用、备份、恢复、销毁的全生命周期管理, 保证关键密钥在各个阶段的安全性。
* **数字签名与验证:**
  * 支持PKCS#1、PKCS#7 Attach、PKCS#7 Detach、PKCS#10、XML等多种签名格式。
  * 支持数据、消息、文件等多种数据类型。
* **数字信封:** 提供基于SM2算法的数字信封功能, 支持PKCS#7标准的数字信封制作及解析。
  ==End of OCR for page 10==

==Start of OCR for page 11==

### 产品工作原理

#### (1) 身份认证场景

* 如场景为系统与系统之间的认证交互, 则系统需要调用签名验签服务器实现签名动作, 验签环节与下述过程一致。

**流程:**

1. **用户(客户端)** 从CA机构获取证书 (存入USBKey)。
2. 用户发起登录请求, 输入**用户名+密码**。
3. **应用系统**验证用户名+密码, 通过后发起挑战。
4. **应用系统**调用**签名验签服务器**接口, 生成**随机数**。
5. **签名验签服务器**返回随机数给应用系统, 应用系统再返回给用户。
6. **用户**调用**USBKey**, 对收到的随机数进行**签名**。
7. **USBKey**返回**数字签名(r, s)**。
8. 用户发起应答, 发送**数字签名**。
9. **应用系统**调用**签名验签服务器**接口, 发送数字签名(r, s)请求验签。
10. **签名验签服务器**结合随机数, **验证签名**。
11. **签名验签服务器**返回验证结果(通过/不通过)给应用系统, 应用系统最终返回登录结果给用户。

#### (2) 完整性校验、不可否认性场景

* 一般只有数据归档时才会做数字签名, 比如云平台归档镜像, 系统的备份文件等。为了区分归档时和用户读取时的文件, 以下用M和M’做区分, 如果没有篡改就是同样的文件。

**流程:**

1. **应用系统**使用SM3计算文件**M的杂凑值**。
2. **应用系统**调用**签名验签服务器**接口, 发送M的杂凑值请求签名。
3. **签名验签服务器**计算**签名值**。
4. **签名验签服务器**返回**数字签名(r, s)**。
5. **应用系统**保存文件签名(r, s)。
6. (一段时间后) **用户**发起文件**M的读取请求**。
7. **应用系统**使用SM3计算文件**M'的杂凑值**。
8. **应用系统**调用**签名验签服务器**接口, 发送M'的杂凑值和文件签名(r, s)请求验签。
9. **签名验签服务器**验证文件签名值。
10. **签名验签服务器**返回验签结果(通过/不通过)。
11. **应用系统**返回读取请求结果(成功/失败)给用户。
    ==End of OCR for page 11==

==Start of OCR for page 12==

### 部署方式及应用场景

签名验证服务器主要为应用系统提供签名/验签等密码运算功能, 与应用系统网络可达, 应用服务器通过API接口调用签名验签服务器, 从而实现相关密码功能的使用。

#### 用户签名场景

* **流程:**
  1. **客户端(持有CA数字证书)** 使用用户数字证书进行签名, 将签名信息发送至**应用服务器**。
  2. **应用服务器**调用**签名验签服务器**接口进行签名验证, 从而保证用户身份的**真实性**和**不可否认性**。
* **架构:** 终端用户 -> 互联网 -> 边界网关 -> 应用系统(集成接口) -> 签名验签服务器

#### 系统签名场景

* **流程:**
  1. **应用系统A**调用**签名验签服务器**接口进行签名, 将交易数据和签名信息发送至**应用系统B**。
  2. **应用系统B**调用**签名验签服务器**接口进行签名验证, 从而保证消息来源的**真实性**和**不可否认性**。
* **架构:** 应用系统A <-> 签名验签服务器 <-> 应用系统B
  ==End of OCR for page 12==

==Start of OCR for page 13==

## 4. 时间戳服务器

**时间戳服务器**通过从国家权威标准时间发布机构获取标准时间, 结合数字证书和数据摘要技术, 为用户的电子数据签发可信、权威的时间戳。在密评中时间戳服务器主要用于响应**应用和数据安全层面**对于**不可否认性**的要求。

### 主要实现效果

* **时间抗抵赖:** 使用数字签名技术, 在时间方面, 防止网上操作行为抵赖、电子数据有效性抵赖。
* **确认网上操作时间相对顺序:** 重要流程中环节执行时间或完成时间的顺序控制。

### 工作流程

1. **应用系统**向**时间戳服务器**提交时间戳签发请求, 请求中包含**原文数据的SM3摘要**。
2. **时间戳服务器**从**NTP时间服务器**或**GPS/北斗**获取权威时间，进行时间同步。
3. **时间戳服务器**将收到的**摘要值**和**当前时间**进行绑定。
4. **时间戳服务器**使用自己的**SM2私钥**对绑定后的数据进行签名，生成**时间戳签名**。
5. **时间戳服务器**将处理结果（时间戳签名）返回给**应用系统**。
   ==End of OCR for page 13==

==Start of OCR for page 14==

## 5. 安全认证网关

**安全认证网关**是采用数字证书为应用系统提供用户管理、身份鉴别、单点登录、传输加密、访问控制和安全审计服务的产品。在密评中, 安全认证网关主要用来响应**网络和通信层面**、**应用和数据安全层面**对**用户身份鉴别**的相关要求。

### 产品核心功能

* **安全传输:** 面向web类应用, 安全认证网关通过反向代理技术代理访问业务系统, 在用户与网关之间建立**安全合规的https连接**, 安全认证网关可根据用户端浏览器情况, **自动适配**具体的TLCP/TLS/SSL协议算法套件(国密/国际), 最终实现业务的安全访问。
* **身份认证:** 安全认证网关可将业务系统身份认证体系与**基于数字签名的身份鉴别机制**相结合, 用户可采用**USBkey(用户数字证书)**的方式登录, 证书信息与账号的对应关系校验成功后即认为认证成功。

### 功能流程

1. **身份认证 (终端 -> 网关):**
   * 用户发起身份验证请求（可触发双因子认证）。
   * 终端（国密/普通浏览器）与网关进行证书校验（SM2/RSA自适应）。
2. **安全传输 (网关 -> 应用):**
   * 网关进行证书自适应和代理转发。
   * 将请求转发至应用通道A或B，并进行访问控制。
3. **应用权限控制 (应用):**
   * 应用进行访问评估，查询资源访问控制列表、黑名单等。
   * 可与LDAP/AD集成。
     ==End of OCR for page 14==

==Start of OCR for page 15==

### 应用场景及部署方式

安全认证网关通常采用**网关工作模式**, 对业务系统进行反向代理。实际部署中可采用**单臂部署、逻辑串接**的方式。在密改项目中一般与**国密浏览器、国密SSL站点证书、用户USBkey**等产品结合使用。

#### 交互体验

* 将应用的域名指向安全认证网关代理地址。
* 安全认证网关配置第三方认证对接, 如LDAP等。
* 用户通过浏览器访问添加的应用, 访问请求转发到安全认证网关, 网关根据访问策略将未认证的请求进行挑战认证。
* 安全认证网关对接第三方认证系统进行用户认证。

#### 安全效果

* **正常登录:** 用户登录安全认证网关, 无感知登录访问业务系统, 无需过多操作即可安全的正常访问。
* **双因子认证:** 对指定用户设置了双因子认证后, 用户需要在终端提交用户证书, 完成认证后自动访问业务系统。
* **异常访问:** 当用户出现认证信息失效、触及安全基线等异常情况, 阻断访问并进行提示。
  ==End of OCR for page 15==


==Start of OCR for page 16==

## 6. SSL VPN安全网关

SSL VPN安全网关是商密应用项目中常见的设备之一, 产品集认证及传输加密功能于一体, 通过使用SSL/TLS协议协商加密算法与加密密钥, 从而防止数据在网络传输中被窃取、篡改, 为客户提供安全、可靠、易用的密码安全服务。

### 产品核心功能

* SSL VPN网关为软硬一体式设备, VPN模式下需要在客户端/服务端安装客户端软件, 可基于虚拟网卡实现安全的VPN隧道。
* 网关工作过程主要是基于SSL实现通信双方的握手(单/双向)。
* 在密评中, SSL VPN安全网关主要用来响应**通信实体身份鉴别、通信过程中数据的完整性、通信过程中重要数据的机密性保护**。
* **1. 原生国密:** 支持SM1/SM2/SM3/SM4算法, 支持国密双证书体系, 内置国密加密卡。
* **2. 高强度传输加密:** 支持SSL/TLS1.1/TLS1.2/TLCP, 支持多种高强度的密码算法套件。
* **3. L3-L7VPN:** 支持L3 VPN实现TCP/UDP的加密传输, 支持L7层应用代理。
* **4. 多因素身份认证:** 提供短信认证、数字证书认证等认证机制。
* **5. 细颗粒度权限管控:** 提供资源白名单、资源访问控制、终端授权访问、IP黑名单等多种权限管控能力。
* **6. 系统设置:** 支持高可用, 支持系统日志、管理日志、VPN事件日志、终端接入日志、资源访问日志等多种日志审计维度。
  ==End of OCR for page 16==

==Start of OCR for page 17==

### 部署方式及应用场景

SSL VPN安全网关使用国密SSL/TLS/TLCP协议, 应用虚拟网卡TUN/TAP驱动来扩展网络, 可兼容多种网络资源类型。面向B/S架构系统可通过SSL代理的方式提供安全能力, 面向C/S架构系统可通过端口转发、网络拓展等方式适配多种协议类型。

* **L7 VPN (应用层代理)**

  * **特点:** 通过SSL代理实现应用系统的安全访问, **无需任何客户端软件**, 为内网的应用系统提供传输加密、身份认证、权限控制等安全访问功能。
  * **适用场景:** B/S架构系统。
  * **架构:** 国密/普通浏览器 <- (国密/普通HTTPS) -> SSL VPN网关 (SSL代理模式) <- (HTTP) -> B/S架构系统。
* **L3/L4 VPN (网络层隧道)**

  * **特点:** 通过L3/L4 VPN (**需要客户端**), 为内网的TCP和UDP应用(如FTP等)提供传输加密、身份认证、权限控制等安全访问功能。
  * **适用场景:** C/S架构系统。
  * **架构:** 客户端(带双因子认证) <- (国密SSL VPN隧道) -> SSL VPN网关 -> (端口转发/网络拓展/文件共享) -> C/S架构系统。
    ==End of OCR for page 17==

==Start of OCR for page 18==

## 7. IPSec VPN网关

IPSec VPN网关系统是为防止数据在传输过程中被第三方获取或篡改的安全产品, 提供安全隧道的协商建立与维护、数据的加解密, 实现企业总部与各个分支机构、合作伙伴之间的远程接入等多种网络互联需求, 同时对这些远程网络中传输的数据提供机密性、完整性等安全防护手段。

### 产品核心功能

* IPSec VPN网关为软硬一体式设备, 基于虚拟网卡建立IPSec VPN隧道。
* 在密评中, IPSec VPN网关主要用来响应**通信实体身份鉴别、通信过程中数据的完整性、通信过程中重要数据的机密性保护**(一般是灾备通道)。
* **原生国密:** 支持SM1/SM2/SM3/SM4算法, 支持国密双证书体系, 内置国密加密卡。
* **高强度传输加密:** 支持国密标准的IKE协商, 支持多种高强度的加密算法套件。
* **异步并行处理:** 支持设备同时与多个安全节点及安利多个安全隧道。
* **监控告警:** 网关可对设备资源不足、离线、授权异常、网络异常、VPN异常等状态的进行告警检测。
* **日志审计:** 支持系统日志、调试日志、IPsecVPN信息日志、错误日志、用户操作日志等多种日志审计维度。
* **WEB管理界面:** 产品提供方便快捷的WEB管理页面对通信策略, 用户、资源控制等进行管理。
  ==End of OCR for page 18==

==Start of OCR for page 19==

### 部署方式及应用场景

* **分支组网:** 各分支机构在各自办公内网即可通过当前IPSec VPN隧道专网就能安全、便捷、低成本的访问企业总部应用资源, 并实现内部业务信息共享, 解决异地办公业务访问问题。
* **备份网络:** 采用IPSec VPN产品构建的备份网络, 相较专线备份网络能大大节省企业成本, 并提供更高的安全性和部署灵活性。
* **部署架构 (站点到站点VPN):**

  * **分支机构侧:** 内部网络 -> IPSec VPN安全网关 (单臂部署) -> Internet
  * **总部园区侧:** Internet -> 防火墙 -> IPSec VPN安全网关 (单臂部署) -> 总部内部网络
  * 分支与总部之间通过 **IPSec VPN隧道** 连接。
    ==End of OCR for page 19==

==Start of OCR for page 20==

# 03 | 应用类商用密码产品介绍

==End of OCR for page 20==

==Start of OCR for page 21==

## 1. 密码服务平台

密码服务平台主要针对云上业务场景, 将密码产品和能力封装为各项密码服务向用户提供, 相比硬件更符合云环境按需分配、弹性扩容的特点, 能够更好的满足用户云上密码应用与合规需求。同时, 通过密码服务平台, 能够实现各类密码服务与密码资源的监测预警、统一调度和管理。

### 产品架构

* **密码资源层:** 包括云服务器密码机、SSL VPN安全网关、签名验签服务器等密码设备, 为上层密码服务提供基础算力支撑。
* **密码服务层:** 通过虚拟化技术进行服务封装, 为云上应用提供包括数据加解密服务、安全通道服务、安全认证服务等服务化密码能力, 灵活满足应用需求。
* **密码管理层:** 作为用户与密码服务平台的交互界面, 可根据用户身份角色提供不同管理功能, 包括服务管理、租户管理、订单管理、监控告警等。
* **API网关:** 为云租户使用密码服务提供统一的API调用接口, 借助API网关, 可以快速地实现密码能力的集成。

*(注：架构图展示了从底层密码资源，到中层通过API网关封装的服务，再到顶层面向租户和运营者的管理门户，以及最终为云上各种应用（PC端、移动端）提供密码服务的全景。)*
==End of OCR for page 21==

==Start of OCR for page 22==

### 服务开通流程

云上密码服务包括承载服务的ECS虚拟机和实现国密运算的VSM虚拟密码机, 租户提交服务申请后, 密码服务平台将自动化完成ECS实例的创建和VSM资源的分配, 具体流程如下:

1. **云租户**在**自服务门户**上**申请开通服务** (如选择密码组件、订阅服务)。
2. 请求生成**订单**, 由**管理员**进行**订单审批**。
3. 审批通过后，**密码服务平台**的**资源管理**模块进行**资源分配**。
4. 平台从**镜像仓库**中**拉取镜像** (密码服务镜像)。
5. 平台根据镜像**创建服务**实例 (为租户A或租户N创建密码服务ECS)。
   * ECS实例中包含如密钥管理系统、协同签名系统、签名验签系统等应用。
   * 同时，平台在底层的**云服务器密码机**上，为该租户分配一个独立的**虚拟密码机 (VSM)** 实例。
     ==End of OCR for page 22==

==Start of OCR for page 23==

## 2. 密码应用监管平台

密码应用监管平台是面向密码管理局等监管部门, 解决对辖区内责任单位系统开展密评工作的进度和监管问题的平台类产品。通过密码应用监管平台, 实现密码应用的“**可管理、可感知、可预警、可处置**”的一体化监管能力, 对网络空间中密码应用进行全面有效监测和管理。

*(Image: A dashboard screenshot of "商用密码应用安全性监管平台", showing maps, charts, and statistics about cryptographic application status.)*

### 核心功能模块

* **全息档案:** 责任单位、密评机构、密码厂商, 资源信息。
* **实时监测:** 服务调用, 密码流量, 密码攻击, 系统模拟。
* **通报处置:** 密码设备, 系统模拟, 模拟交互, 数据演示。
* **教学培训:** 专家讲堂, 密码科普, 政策解读, 全景实验。
* **密评管理:** 密评备案, 结果上传, 信息审核, 批量导出。
* **态势感知:** 综合态势, 密码态势, 密评态势, 安全态势。
* **执法检查:** 任务新建, 密评工具, 任务跟进, 结果统计。
* **考核评价:** 考评任务、考评配置, 考评填报、考评评分。
  ==End of OCR for page 23==

==Start of OCR for page 24==

## 3. 密钥管理系统

密钥管理系统基于国产密码算法, 提供密钥的生成、分发、存储、更新、归档、备份、恢复和销毁等全生命周期管理和密码计算服务, 满足用户单位的业务系统对密钥管理的需求。

### 密钥管理系统产品架构

* **代理层:** 通过设备代理接收和处理分发下来的密钥, 并且调用密钥管理平台层提供的接口, 申请密钥等功能。主要组件包括指令接受/解析、密钥申请、终端密钥存储、SDK接口。
* **平台层:** 主要对密钥的生产, 储存, 封装, 分发等功能的实现。主要组件包括设备注册、证书下发、开机自检、安全连接。
* **系统层:** 密钥管理系统的WEB服务功能, 包括身份认证管理、密钥审计管理、密钥备份恢复管理、密钥储存/归档管理等。主要组件包括密钥管理、设备信息、设备监控。
* **数据存储:** 存放系统运行所需的各类数据。
* **管理角色:** 系统管理员、安全员、审计员。
  ==End of OCR for page 24==

==Start of OCR for page 25==

### 产品工作原理

密钥管理系统在应用功能上支持密钥生成、分发、存储、更新、归档、备份、恢复和销毁等方面的密钥管理与服务需求, 可以作为独立的密钥管理中心使用, 为用户业务系统提供数据加解密、消息摘要等常规的密钥服务。

#### 业务流程

1. **应用服务器**通过API接口向**密钥管理系统**申请密钥。
2. **密钥管理系统**会向**密码卡**(服务器密码机内置密码卡)申请生成密钥。
3. 密码卡生成**原子密钥**后交由密钥管理系统管理。
4. 密钥管理系统将原子密钥**封装/存储**在当前库中。
5. 密钥管理系统响应应用系统的密钥请求, 将封装后的**密钥**给到应用系统。

* **数据流:**
  * **客户端(APP)** 进行加密/解密操作，请求会发送到**应用服务器**。
  * **应用服务器**与**密钥管理系统**交互，进行密钥的申请、请求、分发。
  * **密钥管理系统**与底层的**服务器密码机**交互，进行原子密钥的生成。
  * 密钥在**当前库、历史库、备份库**之间进行同步、归档/销毁。
  * 最终数据以**密文**形式存储。
    ==End of OCR for page 25==

==Start of OCR for page 26==

## 4. 数字证书认证系统

**数字证书认证系统**以公钥基础设施(PKI)为核心, 为应用系统提供数字证书的申请、更新、注销、发布等功能, 为应用系统所有参与个体提供数字证书签发和管理服务, 可协助企业级用户建立符合PKI规范的、灵活通用的应用安全平台。数字证书认证系统主要包括**用户证书发放、证书更新、证书查询注销、证书下载、密钥生成、密钥存储、密钥恢复及证书注销列表签发**等功能。

### PKI体系工作流程

#### 一、用户注册

1. 用户向**RA(注册中心)**提交注册请求，包括用户的身份信息等。
2. **RA管理员**审核用户的请求。如果认证，RA将用户的信息存入**RA数据库**。

#### 二、密钥对生成和证书申请

1. 通过**硬件密码模块**生成密钥对，即公钥和私钥。
2. 用户将含有公钥的**证书签名请求(CSR)**提交给RA(或直接给CA，取决于系统设计)。

#### 三、证书签发

1. RA验证用户提交的CSR，并将验证通过的请求转发给**CA(认证中心)**。
2. **CA管理员**对请求进行审批。审批通过后, CA利用自己的私钥对用户的公钥及其他信息进行签名, 生成**数字证书**。
3. CA管理服务可能涉及证书的生成和签发过程控制。
4. 生成的数字证书存储在**CA数据库**中。

#### 四、证书发布和吊销

1. 数字证书通过电子邮件或其他方式发布给用户, 或者存储在**LDAP目录服务器**上供用户查询下载。
2. 如果需要吊销, **CRL(证书吊销列表)**服务会更新吊销的证书信息, 并公布给所有参与方。
3. **OCSP(在线证书状态协议)**服务用于实时查询证书的状态信息。(CRL的替代方案, 提供更及时的信息)。

#### 五、密钥管理和备份

1. **KM(密钥管理)**服务负责密钥的生成、备份、恢复等管理工作。

#### 六、使用证书

1. 用户在需要证明自己身份或加密通信时, 可以使用由CA签发的数字证书。
2. 通信的另一方通过查询LDAP目录服务器、OCSP服务或检查CRL来验证证书的有效性。
   ==End of OCR for page 26==

==Start of OCR for page 27==

## 5. 协同签名系统

由于手机、pad等移动端无法使用USBKey进行身份认证, 且无安全环境存放私钥, 因此协同签名技术应运而生。协同签名系统主要针对移动端用户身份鉴别的需求, 采用**密钥分割**和**协同签名技术**, 通过移动端和服务端**独立生成和存储密钥分量**, 经过协同运算最终得到完整签名。

### 产品核心功能

* **签名密钥分割:** 采用密钥分割技术, 将签名私钥分割为客户端密钥分量与服务器端密钥分量两部分并分别存储。从生成到运算整个过程中不出现完整的密钥, 从而保证密钥的安全存储。
* **协同签名认证:** 协同签名系统客户端与服务端分别计算各自的部分签名值, 并将各部分签名值进行协同计算最终得到完整签名。
* **设备接入认证:** 支持对接入的终端设备进行审批, 仅允许可信终端接入, 防止非法设备接入系统。
* **日志管理:** 支持对设备操作日志的统计查看, 包括事件类型、事件详情、事件时间维度, 并支持日志下载至本地留存。
* **用户管理:** 内置管理员、操作员、审计员的三权分立账号体系, 通过权限分离提升管理安全性。
  ==End of OCR for page 27==

==Start of OCR for page 28==

### 产品技术原理

协同签名系统实际工作时包括**密钥分割**和**协同签名**两个主要环节:

#### 密钥分割过程

1. 用户登录, 发起设备注册, 申请**协同签名系统服务端证书**。
2. 协同签名系统返回设备证书, 客户端使用内置根证书验证, 确保服务端可信。
3. 客户端查询本地是否有私钥分量文件, 如首次注册, 则生成**私钥分量d1**, 并计算得到**公钥分量P1** (`P1=[d1-1]G`)。
4. 客户端发起协签密钥对生成请求, 发送请求参数、公钥分量P1, 以数字信封的方式发送至服务端。
5. 服务端生成**私钥分量d2**, 使用d2及公钥分量P1计算得出**公钥P** (`P=[d2-1]*P1-G`), 并返回至客户端。
6. 客户端将**私钥分量d1**和**公钥P**存入本地文件, 供后续协同签名运算时使用。

#### 协同签名过程 (见下一页)

==End of OCR for page 28==

==Start of OCR for page 29==

### 产品技术原理 (续)

#### 协同签名过程

1. **移动客户端(SDK模块)** 发起协同签名请求, 本地计算得出消息摘要e及第一部分签名**Q1** (`Q1=k1*G`, k1为随机数), 发送至服务端。
2. **协同签名系统(服务端)** 根据客户端提供的消息摘要e及第一部分签名Q1, 计算得出服务端部分签名**r,s2,s3**, 并返回至客户端。
3. **客户端**使用r,s2,s3以及本地私钥分量**d1**, 计算得出**完整签名(r, s)**。
4. **客户端**使用完整签名发起登录请求, 上传完整签名值到**应用系统**。
5. **应用系统**收到签名值, 向**协同签名系统服务端**发起验证签名请求, 服务端根据签名值返回验证结果, 随后由应用系统向用户返回登录结果。
   ==End of OCR for page 29==

==Start of OCR for page 30==

### 部署方式及应用场景

* 协同签名系统由**协同签名客户端(SDK模块或APP)**、**协同签名系统服务端**两部分组成, 其中协同签名系统服务端通常为软硬一体形态, 协同签名SDK模块需要应用改造集成。
* 协同签名系统工作过程主要包括: **“设备注册 -> 协同签名 -> 使用完整签名访问应用 -> 验签通过”**。
* 在密评中, 协同签名系统主要用来响应**应用和数据安全层面**对**用户身份鉴别**的相关要求(移动访问场景)。

**部署架构:**

1. **移动客户端(集成协同签名SDK模块)** 进行**设备注册**。
2. 客户端与**协同签名系统(服务端)** 进行交互，通过协同签名运算得到**完整签名**。
3. 客户端使用**完整签名**访问**应用系统**。
4. **应用系统**请求**协同签名系统**进行验签，验签通过后用户可正常访问。

* (移动客户端与协同签名系统之间通过互联网、边界网关、交换机连接)
  ==End of OCR for page 30==

==Start of OCR for page 31==

## 6. 电子签章系统

电子签章系统提供电子印章申请、制作、应用、销毁全生命周期的管理, 负责对电子文件进行数据签章保护, 保障文件的**真实性、完整性和签章行为的不可否认性**。在密评中电子签章系统主要用于响应**应用和数据安全层面**对于**不可否认性**的要求。

电子签章系统通常由**印章制发系统**和**印章服务系统**两部分组成:

* **印章制发系统:** 主要提供电子印章的申请、审核、制章、发放等印章管理功能。
* **印章服务系统:** 主要提供电子签章、验章等服务功能。

#### (1) 印章发放流程

1. **用户**登录**电子签章系统(客户端)**。
2. 用户向**电子签章(印章制发系统)**提交**印章申请**。
3. 制发系统完成**印章发放**。

#### (2) 文件签章流程

1. **电子签章系统(客户端)** 准备**待签文件**。
2. 向**电子签章(印章服务系统)** 提交**签章申请**并进行**签章验证**。
3. 客户端进行**文件签章**操作。
4. 生成**已签章文件**。
   ==End of OCR for page 31==

==Start of OCR for page 32==

# 04 | 其它商用密码产品介绍

==End of OCR for page 32==

==Start of OCR for page 33==

## 1. 国密电子门禁系统

国密电子门禁系统主要由门禁卡(国密CPU卡)、国密门禁读卡器、门禁控制器、门禁发卡器、密钥注入器和PCI-E密码卡等硬件设备, 以及门禁管理系统、门禁日志审计系统和密钥管理系统等软件组成, 主要用于响应密评中**物理和环境安全层面**对于进入人员**身份真实性**和进出记录**存储完整性**的要求。

#### 用户身份鉴别实现过程:

1. 国密门禁读卡器读取国密CPU的**卡片信息**(主要是卡片ID), 通过卡片ID计算该国密CPU卡的**卡片密钥K1**。
2. 国密门禁读卡器生成**随机数M1**并回传给国密CPU卡。
3. 国密CPU卡使用密钥K1对M1进行SM4对称加密, 获得**密文M2**并发送至国密门禁读卡器。
4. 国密门禁读卡器以卡片密钥K1对M1进行SM4对称加密获得**密文M3**, 将M3与M2进行对比, 若相等则判定用户身份合法, 否则判定不合法。

#### 门禁进出记录数据完整性保护实现过程:

1. 门禁控制器将用户操作信息传送给**门禁管理主机**。
2. 部署在管理主机上的**日志审计系统**自动生成一条日志记录, 通过内置的**PCI-E密码卡**, 计算该条日志的**HMAC值(计为M1)**, 并将该条日志信息及M1保存至后台数据库。
3. 当用户在日志审计系统中查看该条日志记录时, 日志审计系统从后台数据库中读取该条日志信息及HMAC值M1, 并通过**PCI-E密码卡再次计算**日志记录的**HMAC值(计为M2)**, 将M1与M2进行比对, 如果一致, 则判定该条日志记录正常; 如果不一致, 则提示该条日志记录被篡改。
   ==End of OCR for page 33==

==Start of OCR for page 34==

## 2. 国密视频监控系统

国密视频监控系统主要由国密摄像机、国密NVR、PCI-E密码卡等硬件设备, 以及视频播放客户端等软件组成, 主要用于响应密评中**物理和环境安全层面**对于视频监控音像记录数据**存储完整性**的要求。

#### 音像记录数据完整性保护实现过程:

1. 国密摄像机完成音视频数据采集之后, 由内置的**SD加密卡**对音视频数据进行加密, 通过网络传送至**国密NVR**。
2. 国密NVR对音视频数据进行解密, 并采用**HMAC技术**计算音视频数据的**MAC值(记为M1)**, 将音视频数据和MAC值保存在国密NVR中。
3. 当用户在监控管理主机上通过视频播放客户端软件浏览相关音像记录数据时, 视频播放客户端软件会调用内置的**PCI-E密码卡**, 再次计算该音像记录数据的**MAC值(记为M2)**, 并将M1与M2进行比对, 若一致则判定该数据正常, 若不一致则提示该数据被篡改。
   ==End of OCR for page 34==

==Start of OCR for page 35==

## 3. 智能密码钥匙&数字证书

#### 智能密码钥匙

智能密码钥匙是一种实现密码运算、密钥管理功能, 提供密码服务的终端密码设备, 一般采用无驱动设计, 使用USB接口形态, 因此也称为**USBKey**。

智能密码钥匙主要用于实现数据安全存储和身份认证功能, 具体包括:

* **密码运算:** 支持分组密码算法(SM4)、公钥密码算法(SM2)、密码杂凑算法(SM3)。
* **密钥管理:** 包括生成随机数、密钥的生成、存储、使用、导入、导出、协商等。

#### 数字证书

数字证书是由CA机构发放并经其认证的, 包含拥有者身份信息以及公钥相关信息的一种电子文件, 可以用来证明数字证书持有者的真实身份, 解决相互间的信任问题。

常见的数字证书种类包括:

* **个人身份证书:** CA系统给个人签发的证书, 代表个人身份, 一般存储在智能密码钥匙、IC卡等安全介质中, 可用于安全Web站点访问、网上银行、网上报税等场景。
* **Web身份证书:** 即SSL站点证书, 是CA系统给网站签发的证书, 主要和网站的IP地址、域名绑定, 可以保证网站的真实性。
* **设备证书:** CA系统给软件系统或设备签发的证书, 用于证实设备的身份。Web证书也可以认为是设备证书的一种。
  ==End of ocr for page 35==

==Start of OCR for page 36==

### 数字证书的使用 & 国密双证书体系

#### 数字证书的使用/验证过程

* 用户和应用系统分别获取由权威CA机构签发的个人证书和SSL站点证书。
* 用户访问应用, 会获取网站的SSL证书, 由于**国密浏览器**内置了国密算法的CA根证书, 因此可以验证国密SSL证书的有效性, 而普通浏览器只能验证国际算法签发的SSL证书。
* 在服务端需验证个人用户身份的场景下, 服务端会获得用户的证书并验证其有效性, 同时发起**挑战-应答机制**, 进行身份鉴别。

#### 国密双证书体系

公钥既可以用于加密也可以用于签名, 在加密场景下监管和用户有私钥备份的需求, 但签名场景下不能再有额外的备份。因此我国PKI系统采用双证书体系:

* **签名证书:** 用户在本地生成公私钥对, **私钥自己留存**作为签名私钥, 公钥交给CA作为签名证书。
* **加密证书:** 由**CA生成公私钥对**, **私钥由用户和CA共用**掌握, 对应的证书作为加密证书, 可满足因私钥丢失的信息还原和恢复需求。

**双证书体系图示:**

* 用户**自己生成密钥对A** (保留私钥A)，向CA申请证书。
* **CA生成密钥对B** (保留私钥B)，向用户颁发证书。
* CA使用自己的私钥，分别对用户的公钥A和自己的公钥B进行签名，生成**签名证书A**和**加密证书B**。
* CA将两个证书和加密私钥B（通过用户公钥A加密后）一同返回给用户。
  ==End of OCR for page 36==

==Start of OCR for page 37==

* **Logo 1:** OLYMPPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

## 联系我们 (Contact Us)

浙江省杭州市滨江区西兴街道联慧街188号安恒大厦
www.dbappsecurity.com.cn
400-6059-110

*(Image: QR Code for "安恒信息官方微信")*
安恒信息官方微信
==End of OCR for page 37==
