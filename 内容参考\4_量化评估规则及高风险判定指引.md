# 商用密码培训课程——量化评估规则及高风险判定指引

==Start of OCR for page 1==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

# 商用密码培训课程

## ——量化评估规则及高风险判定指引

**商密产品线**
www.dbappsecurity.com.cn 400-6059-110

*(Image: A modern office building, 安恒大厦)*
==End of OCR for page 1==

==Start of OCR for page 2==
**Header:** 安恒信息 | 构建安全可信的数字世界 (DAS-Security | Build A Secure And Credible Digital World)

# 目录 (Contents)

* **01** 前言
* **02** 量化评估规则(2023版)讲解
* **03** 高风险判定指引讲解
  ==End of OCR for page 2==

==Start of OCR for page 3==

# 01 | 前言

==End of OCR for page 3==

==Start of OCR for page 4==
**Header:** 业内标准与框架 | 安恒信息 | 构建安全可信的数字世界

目前我国密码标准体系技术维度包含密码基础类、基础设施类、密码产品类、应用支撑类、密码应用类、密码检测类、密码管理类共7类;
现有标准围绕技术、产品、特定应用指导为主, **针对系统的密码应用建设, 主要是以GB/T 39786-2021《信息系统密码应用基本要求》(“密评”)作为标准参照。**

### 主要参考标准与文件

* **GB/T 39786-2021** 《信息安全技术信息系统密码应用基本要求》
* **GMT 0116-2021** 《信息系统密码应用测评过程指南》
* **GB/T 43206-2023** 信息安全技术 信息系统密码应用测评要求
* **《商用密码应用安全性评估量化评估规则》**
* **《信息系统密码应用高风险判定指引》**
* **《商用密码应用安全性评估FAQ》**
  ==End of OCR for page 4==

==Start of OCR for page 5==
**Header:** 测评过程主要流程 | 安恒信息 | 构建安全可信的数字世界

在测评活动开展前, 需要对被测信息系统的密码应用方案进行评估, 通过评估的密码应用方案可以作为测评实施的依据。
**测评过程包括四项基本测评活动: 测评准备活动、方案编制活动、现场测评活动、分析与报告编制活动。**
测评方与被测单位之间的沟通与洽谈应贯穿整个测评过程。

1. **测评准备活动**

   * 本活动是开展测评工作的前提和基础, 主要任务是掌握被测信息系统的详细情况, 准备测评工具, 为编制密评方案做好准备。
   * **流程:**
     * 项目启动
     * 信息收集和分析
     * 工具和表单准备
2. **方案编制活动**

   * 本活动是开展测评工作的关键活动, 主要任务是确定与被测信息系统相适应的测评对象、测评指标、测评检查点及测评内容等, 形成密评方案, 为实施现场测评提供依据。
   * **流程:**
     * 测评对象确定
     * 测评指标确定
     * 测评检查点确定
     * 测评内容确定
     * 密评方案编制
3. **现场测评活动**

   * 本活动是开展测评工作的核心活动, 主要任务是根据密评方案分步实施所有测评项目, 以了解被测信息系统真实的密码应用现状, 获取足够的证据, 发现其存在的密码应用安全性问题。
   * **流程:**
     * 现场测评准备
     * 现场测评和结果记录
     * 结果确认和资料归还
4. **分析与报告编制活动**

   * 本活动是给出测评工作结果的活动, 主要任务是根据密评方案和有关要求, 通过单元测评、整体测评、量化评估和风险分析等方法, 找出被测信息系统密码应用的安全保护现状与相应等级的保护要求之间的差距, 并分析这些差距可能导致的被测信息系统所面临的风险, 从而给出各个测评对象的测评结果和被测信息系统的评估结论, 形成密评报告。
   * **流程:**
     * 单元测评 -> 整体测评 -> 量化评估 -> 风险分析 -> 评估结论形成 -> 密评报告编制
       ==End of OCR for page 5==

==Start of OCR for page 6==

# 02 | 量化评估规则(2023版)讲解

==End of OCR for page 6==

==Start of OCR for page 7==
**Header:** 量化评估框架 | 安恒信息 | 构建安全可信的数字世界

参考GM/T0115-2021《信息系统密码应用测评要求》, 从三个方面进行量化评估:

* **D: 密码使用有效性 (Cryptography Deployment effectiveness)**

  * 密码技术是否被**正确、有效使用**, 以满足信息系统的安全需求, 有效提供机密性、完整性、真实性和不可否认性的保护。
* **A: 密码算法/技术合规性 (Cryptography Algorithm/Technique compliance)**

  * 信息系统中使用的**密码算法**是否符合法律、法规的规定和密码相关国家标准、行业标准的有关要求; 信息系统中使用的**密码技术**是否遵循密码相关国家标准和行业标准或经国家密码管理部门核准。
* **K: 密钥管理安全 (Key management security)**

  * **密钥管理的全生命周期**是否安全, 用于密码计算或密钥管理的**密码产品/密码服务**是否安全。

---

### 主要修订情况 (对比2020/2021版)

* **2021版 vs 2020版:**
  * 将“密码使用安全”更名为“密码使用有效性”
  * 将“密码算法/技术安全”更名为“密码算法/技术合规性”
* **2023版 vs 2021版:**
  * **第5章:** 更名为“量化评估规则”，修改了各测评对象的评估规则和量化评估表，增加了合规性修正参数(Ra)和密钥管理安全修正参数(Rk)。
  * **第6章:** 更名为“量化评估阈值”，并根据新规则更新了阈值。
    ==End of OCR for page 7==

==Start of OCR for page 8==
**Header:** 测评单元权重表 | 安恒信息 | 构建安全可信的数字世界

*(注：这是一个非常密集的表格，以下为结构化转录)*

| 要求维度                                             | 安全层面序号 i | 安全层面                 | 测评单元序号 j | 测评单元                         | 安全层面权重 (wi) | 指标权重 (wij)        |
| :--------------------------------------------------- | :------------- | :----------------------- | :------------- | :------------------------------- | :---------------- | :-------------------- |
| **密码应用技术要求**                           | 1              | **物理和环境安全** | (1)            | 身份鉴别                         | 10                | 0.4 / 0.7 / 1 / 1     |
|                                                      |                |                          | (2)            | 电子门禁记录数据存储完整性       |                   | 0.4 / 0.4 / 0.7 / 0.7 |
|                                                      |                |                          | (3)            | 视频记录数据存储完整性           |                   | / / 0.7 / 0.7         |
|                                                      | 2              | **网络和通信安全** | (1)            | 身份鉴别                         | 20                | 0.4 / 0.7 / 1 / 1     |
|                                                      |                |                          | (2)            | 通信数据完整性                   |                   | 0.4 / 0.4 / 0.7 / 1   |
|                                                      |                |                          | (3)            | 通信过程中重要数据的机密性       |                   | 0.4 / 0.7 / 1 / 1     |
|                                                      |                |                          | (4)            | 网络边界访问控制信息的完整性     |                   | 0.4 / 0.4 / 0.4 / 0.7 |
|                                                      |                |                          | (5)            | 安全接入认证                     |                   | / / 0.4 / 0.7         |
|                                                      | 3              | **设备和计算安全** | (1)            | 身份鉴别                         | 10                | 0.4 / 0.7 / 1 / 1     |
|                                                      |                |                          | (2)            | 远程管理通道安全                 |                   | / / 1 / 1             |
|                                                      |                |                          | (3)            | 系统资源访问控制信息完整性       |                   | 0.4 / 0.4 / 0.4 / 0.7 |
|                                                      |                |                          | (4)            | 重要信息资源安全标记完整性       |                   | / / 0.4 / 0.7         |
|                                                      |                |                          | (5)            | 日志记录完整性                   |                   | 0.4 / 0.4 / 0.4 / 0.7 |
|                                                      |                |                          | (6)            | 重要可执行程序完整性、来源真实性 |                   | / / 0.7 / 1           |
|                                                      | 4              | **应用和数据安全** | (1)            | 身份鉴别                         | 30                | 0.4 / 0.7 / 1 / 1     |
|                                                      |                |                          | (2)            | 访问控制信息完整性               |                   | 0.4 / 0.4 / 0.4 / 0.7 |
|                                                      |                |                          | (3)            | 重要信息资源安全标记完整性       |                   | / / 0.4 / 0.7         |
|                                                      |                |                          | (4)            | 重要数据传输机密性               |                   | 0.4 / 0.7 / 1 / 1     |
|                                                      |                |                          | (5)            | 重要数据存储机密性               |                   | 0.4 / 0.7 / 1 / 1     |
|                                                      |                |                          | (6)            | 重要数据传输完整性               |                   | 0.4 / 0.7 / 0.7 / 1   |
|                                                      |                |                          | (7)            | 重要数据存储完整性               |                   | 0.4 / 0.7 / 0.7 / 1   |
|                                                      |                |                          | (8)            | 不可否认性                       |                   | / / 1 / 1             |
| **密码应用管理要求**                           | 5              | **管理制度**       | (1)            | 具备密码应用安全管理制度         | 8                 | 1 / 1 / 1 / 1         |
|                                                      |                |                          | (2)            | 密钥管理规则                     |                   | 0.7 / 0.7 / 0.7 / 0.7 |
|                                                      |                |                          | (3)            | 建立操作规程                     |                   | / 0.7 / 0.7 / 0.7     |
|                                                      |                |                          | (4)            | 定期修订安全管理制度             |                   | / / 0.7 / 0.7         |
|                                                      |                |                          | (5)            | 明确管理制度发布流程             |                   | / / 0.7 / 0.7         |
|                                                      |                |                          | (6)            | 制度执行过程记录留存             |                   | / / 0.7 / 0.7         |
|                                                      | 6              | **人员管理**       | (1)            | 了解并遵守密码相关法律法规       | 8                 | 0.7 / 0.7 / 0.7 / 0.7 |
|                                                      |                |                          | (2)            | 建立密码应用岗位责任制度         |                   | / 1 / 1 / 1           |
|                                                      |                |                          | (3)            | 建立上岗人员培训制度             |                   | / 0.7 / 0.7 / 0.7     |
|                                                      |                |                          | ...            | ...                              |                   | ...                   |
| *(注: 表格内容过于庞大，此处仅展示部分结构和内容)* |                |                          |                |                                  |                   |                       |
| ==End of OCR for page 8==                            |                |                          |                |                                  |                   |                       |

==Start of OCR for page 9==
**Header:** 测评量化评估规则: 量化评估表 | 安恒信息 | 构建安全可信的数字世界

| 序号                      | 符合情况           | 密码使用有效性 (D)                                                                                                                                                               | 密码算法/技术合规性 (A) | 密钥管理安全 (K) | 解释说明                                                                                                                      | 示例                                                                  | 分值 |
| :------------------------ | :----------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------- | :--------------- | :---------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------- | :--- |
| **1**               | **符合**     | √                                                                                                                                                                               | √                      | √               | 全部符合相关的要求。                                                                                                          | 全部符合                                                              | 1    |
| **2**               | **部分符合** | √                                                                                                                                                                               | ×                      | √               | 密码使用有效, 具备安全的密钥管理机制, 但使用的密码算法/技术不符合法律法规的规定和密码相关国家标准、行业标准的有关规定。       | 使用认证合格的密码产品, 未使用国产密码算法/技术                       | 0.5  |
| **3**               | **部分符合** | √                                                                                                                                                                               | √                      | ×               | 密码使用有效, 使用的密码算法/技术符合法律法规的规定和密码相关国家标准、行业标准的有关规定, 但是相关的密钥管理机制存在问题。   | 使用未经认证或者不满足安全等级要求的密码产品, 使用国产密码算法/技术   | 0.5  |
| **4**               | **部分符合** | √                                                                                                                                                                               | ×                      | ×               | 密码使用有效, 但使用的密码算法/技术不符合法律法规的规定和密码相关国家标准、行业标准的有关规定, 相关的密钥管理机制也存在问题。 | 使用未经认证或者不满足安全等级要求的密码产品, 未使用国产密码算法/技术 | 0.25 |
| **5**               | **不符合**   | ×                                                                                                                                                                               | /                       | /                | 未使用密码技术, 或由于未正确、有效使用密码技术导致无法满足信息系统的安全需求。                                                | 使用的密码技术无法满足信息系统的安全需求, 或未使用密码技术等          | 0    |
| **6**               | **注:**      | 在判定密码使用有效性、密码算法/技术合规性、密钥管理安全三个维度时, 均进行独立判定。例如: 在单独判定密码使用有效性维度时, 不考虑由于密码算法/技术合规性、密钥管理安全导致的风险。 |                         |                  |                                                                                                                               |                                                                       |      |
| ==End of OCR for page 9== |                    |                                                                                                                                                                                  |                         |                  |                                                                                                                               |                                                                       |      |

==Start of OCR for page 10==
**Header:** 商用密码应用安全性评估量化评估规则 | 安恒信息 | 构建安全可信的数字世界

### 表1 量化评估表 (简化版)

| 符合情况           | D | A | K | 示例                                                    | 分值 (Si,j,k) |
| :----------------- | :-: | :-: | :-: | :------------------------------------------------------ | :------------ |
| **符合**     | √ | √ | √ | 全部符合相关的要求                                      | 1             |
| **部分符合** | √ | × | √ | 密码使用有效, 具备安全的密钥管理机制, 但算法/技术不合规 | 0.5Ra         |
| **部分符合** | √ | √ | × | 密码使用有效, 算法/技术合规, 但密钥管理存在问题         | 0.5Rk         |
| **部分符合** | √ | × | × | 密码使用有效, 但算法/技术不合规且密钥管理存在问题       | 0.25RaRk      |
| **不符合**   | × | / | / | 未使用密码技术, 或使用无效                              | 0             |

### 表2 密码算法/技术合规性修正参数Ra取值表

| Ra                     | 0.2                                                     | 0.5                                                                                             | 1                                                                                                               |
| :--------------------- | :------------------------------------------------------ | :---------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------- |
| **技术使用情况** | 算法/技术与安全要求不匹配 (如: DES, MD5, SHA-1, RSA512) | 算法/技术可在一定程度上提供安全保障 (安全强度 < 112比特, 如: RSA-1024, 3DES(2密钥), ECDSA-P192) | 算法/技术可较好地提供安全保障 (安全强度 >= 112比特, 如: 3DES(3密钥), AES128+, SHA-224+, RSA-2048+, ECDSA-P224+) |

### 表3 密钥管理安全修正参数Rk取值表

| Rk                         | 1 (不修正) | 1.2                                                                     | 1.5      | 1 (不修正)                                                              |
| :------------------------- | :--------- | :---------------------------------------------------------------------- | :------- | :---------------------------------------------------------------------- |
| **安全级别**         | 一级/二级  | 三级                                                                    | 四级     |                                                                         |
| **密钥管理情况**     | /          | 使用一级密码模块, 且满足GM/T 0115-2021中“5.5密钥管理安全性”的其他要求 | 其他情况 | 使用二级密码模块, 且满足GM/T 0115-2021中“5.5密钥管理安全性”的其他要求 |
| ==End of OCR for page 10== |            |                                                                         |          |                                                                         |

==Start of OCR for page 11==
**Header:** 各个层面测评对象识别和确定 | 安恒信息 | 构建安全可信的数字世界

| 安全层面                   | 测评对象                                                                                                                                                                                                                                                                                                                                  |
| :------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **物理和环境安全**   | - 被测信息系统所涉及的所有**物理机房** `<br>`- 具体为机房的**电子门禁系统**和**视频监控系统**                                                                                                                                                                                                                         |
| **网络和通信安全**   | - 针对**跨网络访问的通信信道**，这里的跨网络访问指的是从不受保护的网络区域访问被测系统。`<br>`- 如：非国密浏览器与对外应用系统间的通信信道、国密浏览器与对外应用系统间的通信信道、国密浏览器与后台应用系统间的通信信道、IPSec VPN之间通信信道、政务外网VPN客户端与内网SSL VPN间通信信道、VPN客户端与运维SSL VPN间通信信道（运维） |
| **设备和计算安全**   | - 主要包括**通用服务器**（如应用服务器、数据库服务器）、数据库管理系统、**整机类和系统类的密码产品、堡垒机**等                                                                                                                                                                                                                |
| **应用和数据安全**   | - 被测系统的所有**业务应用**                                                                                                                                                                                                                                                                                                        |
| ==End of OCR for page 11== |                                                                                                                                                                                                                                                                                                                                           |

==Start of OCR for page 12==
**Header:** 测评对象评分Sijk | 安恒信息 | 构建安全可信的数字世界

*(本页通过一个具体示例解释如何根据量化评估表得出单个测评对象的得分)*

### 场景

* 某信息系统在**网络与通信安全层面**的某一测评对象为“远程办公用户与SSLVPN网关之间的通信信道”，其“**身份鉴别**”实现是：
  1. 使用的SSL VPN网关、用户智能密码钥匙密码产品均具有**认证证书**。
  2. 使用**ECC_SM4_CBC_SM3(0xe013)密码套件**, 具体使用**SM2算法**进行身份鉴别。
  3. SSL VPN配置正确, 通信时使用双证书(且为SM2证书)。

### 评分

* **D (有效性):** √ (有效)
* **A (合规性):** √ (合规，使用国密算法)
* **K (密钥管理):** √ (合规，使用认证合格的密码产品)

### 结论

* 该测评对象为**符合**，该测评对象得分为 **1**。
  ==End of OCR for page 12==

==Start of OCR for page 13==
**Header:** 测评对象评分Sijk | 安恒信息 | 构建安全可信的数字世界

*(本页通过另一个示例解释如何使用修正参数)*

### 场景

* 某信息系统在**网络与通信安全层面**的某一测评对象为“远程办公用户与SSLVPN网关之间的通信信道”，其“**身份鉴别**”实现是：
  1. 使用的SSL VPN网关、用户智能密码钥匙密码产品均具有**认证证书**。
  2. 使用**RSA_AES_CBC_SHA256密码套件**, 具体使用**RSA-2048算法**进行身份鉴别。
  3. SSL VPN配置正确, 通信时使用RSA-2048证书。

### 评分

* **D (有效性):** √ (有效)
* **A (合规性):** × (不合规，未使用国产密码算法/技术)。根据表2，RSA-2048安全强度>=112比特，**Ra取值为1**。
* **K (密钥管理):** √ (合规，使用认证合格的密码产品)。

### 结论

* 该测评对象为**部分符合**，根据量化评估表第2条规则，得分为 **0.5 * Ra = 0.5 * 1 = 0.5**。
  ==End of OCR for page 13==

==Start of OCR for page 14==
**Header:** 测评对象评分Sijk | 安恒信息 | 构建安全可信的数字世界

*(本页通过更复杂的示例解释多个修正参数的应用)*

### 场景1

* 信息系统使用了**比要求低级的、但认证合格**的密码产品 (如四级系统使用二级密码模块)。
* **结论:** 判定为部分符合。根据表3，四级系统使用二级模块，**Rk取值为1.5**。该测评对象得分为 **0.5 * Rk = 0.75**。相对旧版(得0.5分)，新版提升了分数。

### 场景2

* 信息系统使用了**比要求低级的、且认证合格**的密码产品 (如四级系统使用二级密码模块, **Rk=1.5**)，且使用的**不合规的密码算法/技术** (如RSA-2048, **Ra=1**)。
* **结论:** 匹配量化评估表第4条规则，判定为部分符合，该测评对象得分为 **0.25 * Rk * Ra = 0.25 * 1.5 * 1 = 0.375**。

### 场景3

* 使用的密码技术**无法满足**信息系统的安全需求，或**未使用**密码技术。
* **结论:** 判定为**不符合**，该测评对象得分为 **0**。
  ==End of OCR for page 14==

==Start of OCR for page 15==
**Header:** 测评对象评分Sijk - 示例 | 安恒信息 | 构建安全可信的数字世界

*(这是一个通过表格展示评分过程的示例)*

#### 密码应用技术要求量化示例

| 网络与通信层面测评指标                 | 测评对象                                          | D | A | K | 测评对象评分Sijk |
| :------------------------------------- | :------------------------------------------------ | :-: | :-: | :-: | :--------------- |
| **身份鉴别**                     | 远程办公用户与 SSL VPN 网关之间的通信信道         | √ | √ | √ | **1**      |
|                                        | 驻外办事处用户与信息系统之间的 IPSec VPN 通信信道 | √ | × | √ | **0.5**    |
| **通信数据完整性**               | 远程办公用户与 SSL VPN 网关之间的通信信道         | √ | √ | √ | **1**      |
|                                        | 驻外办事处用户与信息系统之间的 IPSec VPN 通信信道 | √ | √ | × | **0.5**    |
| **通信过程中重要数据的机密性**   | 远程办公用户与 SSL VPN 网关之间的通信信道         | √ | √ | √ | **1**      |
|                                        | 驻外办事处用户与信息系统之间的 IPSec VPN 通信信道 | √ | × | × | **0.25**   |
| **网络边界访问控制信息的完整性** | 远程办公用户与 SSL VPN 网关之间的通信信道         | √ | √ | √ | **1**      |
|                                        | 驻外办事处用户与信息系统之间的 IPSec VPN 通信信道 | × | × | × | **0**      |
| **安全接入认证**                 | 指标不适用                                        | / | / | / | **/**      |

==End of OCR for page 15==

==Start of OCR for page 16==
**Header:** 测评单元得分Sij | 安恒信息 | 构建安全可信的数字世界

测评单元得分(Sij)的计算方式为：**单元内各个测评对象的算数平均值**。

**计算公式:**
`Sij = (Sijk1 + Sijk2 + ... + Sijkn) / n`

**示例计算 (基于P15的数据):**

* **身份鉴别单元得分:**

  * (1 + 0.5) / 2 = **0.75**
* **通信数据完整性单元得分:**

  * (1 + 0.5) / 2 = **0.75**
* **通信过程中重要数据的机密性单元得分:**

  * (1 + 0.25) / 2 = **0.625**
* **网络边界访问控制信息的完整性单元得分:**

  * (1 + 0) / 2 = **0.5**
* **安全接入认证单元得分:**

  * 指标不适用，不计分。
    ==End of OCR for page 16==

==Start of OCR for page 17==
**Header:** 各安全层面得分 | 安恒信息 | 构建安全可信的数字世界

安全层面得分的计算方式为：**该层面下所有适用测评单元得分的加权平均值**。

**计算公式:**
`Si = (Σ(Sij * Wij)) / (ΣWij)`

**示例计算 (网络与通信层面):**

| 测评单元           | 测评单元得分(Sij) | 权重(Wij)    |
| :----------------- | :---------------- | :----------- |
| 身份鉴别           | 0.75              | 1            |
| 通信数据完整性     | 0.75              | 0.7          |
| 重要数据机密性     | 0.625             | 1            |
| 边界访问控制完整性 | 0.5               | 0.4          |
| 安全接入认证       | 不适用            | 0.4 (不计入) |

**计算过程:**
(0.75 * 1 + 0.75 * 0.7 + 0.625 * 1 + 0.5 * 0.4) / (1 + 0.7 + 1 + 0.4)
= (0.75 + 0.525 + 0.625 + 0.2) / 3.1
= 2.1 / 3.1
≈ **0.6774**

*注: 每一安全层面的不适用测评指标不计入分值, 平均分摊至该层面其他适用的测评指标分值中, 不会影响其他安全层面得分。*
==End of OCR for page 17==

==Start of OCR for page 18==
**Header:** 整体得分 | 安恒信息 | 构建安全可信的数字世界

整体得分的计算方式为：**所有安全层面得分的加权平均值**。

**计算公式:**
`S = (Σ(Si * Wi)) / (ΣWi)`

**示例计算 (假设各层面得分如下):**

| 安全层面 | 安全层面得分(Si) | 权重(Wi) |
| :------- | :--------------- | :------- |
| 物理环境 | 0.5              | 10       |
| 网络通信 | 0.6774           | 20       |
| 设备计算 | 0.3499           | 10       |
| 应用数据 | 0.8509           | 30       |
| 管理制度 | 0.5222           | 8        |
| 人员管理 | 0.6316           | 8        |
| 建设运行 | 0.6351           | 8        |
| 应急处置 | 0.8541           | 6        |

**整体得分 S =**
(0.5\*10 + 0.6774\*20 + 0.3499\*10 + 0.8509\*30 + 0.5222\*8 + 0.6316\*8 + 0.6351\*8 + 0.8541\*6) / 100
= (5 + 13.548 + 3.499 + 25.527 + 4.1776 + 5.0528 + 5.0808 + 5.1246) / 100
= 67.01 / 100
= **67.01**
==End of OCR for page 18==

==Start of OCR for page 19==

# 03 | 高风险判定指引讲解

==End of OCR for page 19==

==Start of OCR for page 20==
**Header:** 信息系统密评建设标准 | 安恒信息 | 构建安全可信的数字 world

*(这是一个展示GB/T 39786-2021标准框架和高风险项分布的思维导图)*

### GB/T 39786-2021《信息系统密码应用基本要求》框架

* **通用要求**
  * 密码算法合规性
  * 密码技术合规性
  * 密码产品合规性
  * 密码服务合规性
* **技术要求 (70分)**
  * **物理和环境 (10分)**
    * **身份鉴别 (高风险项)**
    * 电子门禁记录数据存储完整性
    * 视频监控音像记录存储完整性
  * **网络和通信 (20分)**
    * **身份鉴别 (高风险项-无缓解措施)**
    * 通信过程数据完整性
    * **通信过程中重要数据机密性 (高风险项)**
    * 访问边界访问控制完整性
    * 安全接入认证
  * **设备和计算 (10分)**
    * **身份鉴别 (高风险项)**
    * **远程管理通道安全 (高风险项)**
    * 系统资源访问控制信息完整性
    * 重要信息资源安全标记完整性
    * 日志记录完整性
    * 重要可执行程序完整性来源真实性
  * **应用和数据 (30分)**
    * **身份鉴别 (高风险项)**
    * 访问控制信息完整性
    * 重要信息资源安全标记完整性
    * **重要数据传输过程机密性 (高风险项)**
    * **重要数据存储过程机密性 (高风险项-无缓解措施)**
    * 重要数据传输过程完整性
    * **重要数据存储过程完整性 (高风险项)**
    * **不可否认性 (高风险项-无缓解措施)**
* **管理要求 (30分)**
  * 管理制度
  * 人员管理
  * 建设运行
  * 应急处置

---

* **技术要求:** 7应14宜1可, **标红为高风险项**, **标星为高风险项且无缓解措施**(三级系统为例)
* **评估结论:** 60~99分 + 无高风险项 = **基本符合**
  ==End of OCR for page 20==

==Start of OCR for page 21==

### 通用要求-密码算法 (高风险)

* **a) 指标要求:** 信息系统中使用的密码算法应符合法律、法规的规定和密码相关国家标准、行业标准的有关要求。
* **b) 适用范围:** 所有级别信息系统。
* **c) 安全问题:**
  1. 使用存在安全问题或安全强度不足的密码算法对重要数据进行保护, 如**MD5、DES、SHA-1、RSA(不足2048比特)**等密码算法;
  2. 使用安全性未知的密码算法, 如自行设计的密码算法、未经安全性论证的密码算法。
* **d) 可能的缓解措施:** 无。
* **e) 风险评价:** 上述任一安全问题一旦被威胁利用后, 可能会导致信息系统面临高风险。
  ==End of OCR for page 21==

==Start of OCR for page 22==

### 通用要求-密码技术 (高风险)

* **a) 指标要求:** 信息系统中使用的密码技术应遵循密码相关国家标准和行业标准。
* **b) 适用范围:** 所有级别信息系统。
* **c) 安全问题:**
  1. 使用存在缺陷或有安全问题警示的密码技术, 如**SSH 1.0、SSL 2.0、SSL 3.0、TLS 1.0**等;
  2. 使用安全性未知的密码技术, 如自行设计的密码通信协议、未经安全性论证的密码通信协议等。
* **d) 可能的缓解措施:** 无。
* **e) 风险评价:** 上述任一安全问题一旦被威胁利用后, 可能会导致信息系统面临高风险。
  ==End of OCR for page 22==

==Start of OCR for page 23==

### 通用要求-密码产品和密码服务 (高风险)

* **a) 指标要求:** 信息系统中使用的密码产品、密码服务应符合法律法规的相关要求。
* **b) 适用范围:** 所有级别信息系统。
* **c) 安全问题:**
  1. 使用自实现且未提供安全性证据的密码产品;
  2. 使用的密码产品存在高危安全漏洞, 如存在**Heartbleed漏洞的OpenSSL**;
  3. 密码产品的使用不满足其安全运行的前提条件, 如其安全策略或使用手册说明的部署条件;
  4. 使用的密码服务, 其密码服务提供商不具有相关资质;
  5. 存在可能会对密钥管理造成严重安全隐患的安全问题。
* **d) 可能的缓解措施:** 无。
* **e) 风险评价:** 上述任一安全问题一旦被威胁利用后, 可能会导致信息系统面临高风险。
  ==End of OCR for page 23==

... (后续页面为各层面高风险项的详细解读，将遵循上述格式进行转录)

==Start of OCR for page 37==
**Header:** 密钥管理安全问题 | 安恒信息 | 构建安全可信的数字世界

#### a) 密钥产生

密钥产生环节可能会对密钥管理造成严重安全隐患的安全问题主要包括:

1. 未采用通过认证的随机数发生器生成密钥、派生密钥或密钥协商过程中的随机值, 且无公开文献和证据证明随机数发生器的合理性和正确性;
2. 密钥在不可控的环境中生成;
3. 密钥协商之前或协商过程中没有验证对方身份真实性。

#### b) 密钥分发

密钥分发环节可能会对密钥管理造成严重安全隐患的安全问题主要包括:

1. 使用没有访问控制机制的存储介质(如普通信封、普通U盘)等传输明文密钥, 且管理制度无法保证密钥在分发过程中的安全性;
2. 密钥在不可控的环境中分发时, 未使用密码技术保护密钥的机密性和完整性。

#### c) 密钥存储、备份和归档

密钥存储、备份和归档环节可能会对密钥管理造成严重安全隐患的安全问题主要包括:

1. 密钥(除公钥外)以明文形式存储/备份/归档在不可控的环境中, 且可以被非授权的访问、使用、泄露、修改和替换;
2. 公钥存储/备份/归档在不可控的环境中, 且可以被非授权的修改和替换;
3. 用于加密密钥的口令以明文形式存储或复杂度小于10^12。

#### d) 密钥使用

密钥使用环节可能会对密钥管理造成严重安全隐患的安全问题主要包括:

1. 在多个实体可以使用密钥的场景下, 缺乏对密钥的使用控制机制;
2. 对称密钥使用过程中, 由于使用不当导致密钥泄露;
3. 公钥与实体之间无任何关联关系;
4. 公钥与实体之间利用PKI技术进行关联, 但使用前未验证公钥有效性或验证机制不完备;
5. 未按密钥用途正确使用。

#### e) 密钥更新

密钥更新环节可能会对密钥管理造成严重安全隐患的安全问题主要包括:

1. 未建立密钥已泄露或存在泄露风险时的密钥更新机制。

#### f) 密钥销毁和撤销

密钥销毁和撤销环节可能会对密钥管理造成严重安全隐患的安全问题主要包括:

1. 不具备密钥在应急或按需的密钥销毁/撤销的机制;
2. 未按照设定的机制进行密钥销毁/撤销。

#### g) 密钥恢复

密钥恢复环节可能会对密钥管理造成严重安全隐患的安全问题主要包括:

1. 密钥在恢复使用时没有鉴别机制, 可以被导入到其他系统中。
   ==End of OCR for page 37==

==Start of OCR for page 38==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

## 联系我们 (Contact Us)

浙江省杭州市滨江区西兴街道联慧街188号安恒大厦
www.dbappsecurity.com.cn
400-6059-110

*(Image: QR Code for "安恒信息官方微信")*
安恒信息官方微信
==End of OCR for page 38==
