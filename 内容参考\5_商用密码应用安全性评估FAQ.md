# 商用密码培训课程——商用密码应用安全性评估FAQ

==Start of OCR for page 1==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

# 商用密码培训课程

## ——商用密码应用安全性评估FAQ

**商密产品线**
www.dbappsecurity.com.cn 400-6059-110

*(Image: A modern office building, 安恒大厦)*
==End of OCR for page 1==

==Start of OCR for page 2==
**Header:** 安恒信息 | 构建安全可信的数字世界 (DAS-Security | Build A Secure And Credible Digital World)

# 目录 (Contents)

* **01** 通用类
* **02** 密码应用技术类
* **03** 密码应用管理类
* **04** 量化评估类
* **05** 风险判定类
* **06** 特殊场景类
  ==End of OCR for page 2==

==Start of OCR for page 3==
**Header:** 密码相关政策标准 | 安恒信息 | 构建安全可信的数字世界

### 法律

* 密码法
* 网络安全法
* 数据安全法
* 个人信息保护法

### 法规

* 商用密码管理条例
* 商用密码检测机构管理办法
* 商用密码应用安全性评估管理办法

### 国标

* **GB/T39786-2021:** 信息系统密码应用基本要求
* **GB/T43206-2023:** 信息系统密码应用测评要求
* **GB/T43207-2023:** 信息系统密码应用设计指南
* **GB/T22240-2020:** 网络安全等级保护定级指南

### 行标

* **GM/T0054-2018:** 信息系统密码应用基本要求 (被GB/T39786取代)
* **GM/T0115-2021:** 信息系统密码应用测评要求 (被GB/T43206取代)
* **GM/T0116-2021:** 信息系统密码应用测评过程指南

### 指导性文件

* 量化评估规则 (2023版)
* 高风险判定指引
* 商用密码应用安全性评估报告模板 (2023版)
* **商用密码应用安全性评估FAQ (第三版)**
  ==End of OCR for page 3==

==Start of OCR for page 4==
**Header:** 《商用密码应用安全性评估FAQ(第三版)》| 安恒信息 | 构建安全可信的数字世界

中国密码学会密评联委会组织修订的《商用密码应用安全性评估FAQ》(第三版)于**2023年10月**发布, 该文件对商用密码应用安全性评估工作及相关标准中涉及的常见问题进行了整理和解答, 以帮助相关人员更好开展商用密码应用与安全性评估工作。

* **01 版本说明:** 本版本已全部包含之前版本的内容。
* **02 主要修订:** 将问题分为六大类, 全文共解答36个问题。

#### 修订记录时间线

* **2021年12月:** 第一版发布
* **2022年8月:** 第二版发布
* **2023年10月:** 第三版发布

*(Image: Screenshot of the Chinese Association for Cryptologic Research (CACR) website announcing the FAQ release.)*
==End of OCR for page 4==

==Start of OCR for page 5==

# 01 | 通用类

==End of OCR for page 5==

==Start of OCR for page 6==

### 问题1: 信息系统密码应用基本要求的等级

**Q: 如何确定信息系统密码应用的测评等级？**

> **A: 结论是优先与等保定级保持一致性。**
>
> **逻辑流程:**
>
> 1. 首先判断系统是否已完成“网络安全等级保护备案”。
> 2. **已完成备案:**
>    * **最终遵循备案等级:** 密评等级应与等保备案等级保持一致。
> 3. **未完成备案:**
>    * **优先遵循拟定级等级:** 测评时参考系统的拟定级。
>    * 如果系统**无拟定级**，则密码应用等级**至少应为三级**。
>
> **特殊情况:**
>
> * **可以高于等保定级:** 针对被测单位以高安全标准规划、建设信息系统的密码应用, 并希望能够按照GB/T39786-2021中更高密码应用等级进行密评的情形, 密评机构在测评前, 应在确认该做法符合被测信息系统所属密码管理部门相关要求的前提下, 加以确认其密码应用等级是否**等于或者高于**等级保护备案等级, 进而开展测评工作。
>   ==End of OCR for page 6==

==Start of OCR for page 7==

### 问题2: 应、宜、可测评指标把握

**Q: 如何把握GB/T39786中“应”、“宜”、“可”指标的测评要求？**

> **A: “应”和“可”含义明确，核心在于如何处理“宜”指标。**
>
> **【背景说明】** GB/T39786对密码应用各项指标要求的力度, 主要通过**应、宜、可**加以区分。在应、宜、可三个指标中, 可和应的含义明确, 但宜含义, 在GB/T39786中有其特殊性。
>
> **判定流程:**
>
> 1. **是否将“宜”的指标要求纳入标准符合性测评范围？**
>    * “应”指标：**必须**纳入。
>    * “可”指标：**不强制**纳入。
> 2. **处理“宜”指标：**
>    * **情况一: 未编制密码应用方案, 或方案中未对“宜”的指标做明确说明。**
>      * **判定:** 不满足要求，**必须纳入标准符合性测评范围**。
>    * **情况二: 已编制密码应用方案且方案通过评估, 且方案明确不适用“宜”指标项并有对应的风险控制措施说明。**
>      * **测评人员核实:** 检查风险控制措施的适用条件、在实际信息系统中是否被满足、系统实施情况与方案是否一致。
>      * **核实结果 - 满足:** 该指标项为**“不适用”**。
>      * **核实结果 - 不满足:** **必须纳入标准符合性测评范围**。
>        ==End of OCR for page 7==

==Start of OCR for page 8==

### 问题3: 具有认证证书的商用密码产品对应的模块等级

**Q: (修订问题) 未标注密码模块安全等级的商用密码产品在测评时如何判定？**

> **A: 分为“换证”和“新发证书”两种情况处理。**
>
> **背景:** 根据国家密码管理局和市场监管总局联合发布的《关于调整商用密码产品管理方式的公告》(第39号), 自2020年7月1日起, 已发放的《商用密码产品型号证书》自动失效。持证单位可在有效期内自愿申请转换为国推商用密码产品认证证书。
>
> **判定流程:**
>
> * **情况一：换证的密码产品**
>
>   1. 要求密码厂商提供**换证前**的《商用密码产品型号证书》。
>   2. 检查旧证书中是否**标注了其符合的密码模块等级**。
>   3. **是:** 按旧**证书标注的等级**判定。
>   4. **否:** 按**“一级模块”**判定。
> * **情况二：新发证书的密码产品**
>
>   1. 查阅市场监管总局和国家密码管理局发布的《商用密码产品认证目录》。
>   2. 如果产品属于**密码模块标准适用的密码产品** (如服务器密码机、时间戳服务器等)，则其认证过程依据GM/T0028《密码模块安全技术要求》进行检测认证，证书上会体现模块等级。
>   3. 如果产品属于**其他种类产品** (如安全芯片、密码系统类产品等)，则**不依据密码模块标准进行检测和认证**，其合规性按产品自身标准判定。
>      ==End of OCR for page 8==


==Start of OCR for page 9==

### 问题4: 经认证合格的密码产品的产品合规性、密钥安全符合性的判定要点

**Q 4.1 (修订问题): 在密评时, 发现商用密码产品证书过期的情况, 即密评开展时间在密码产品认证证书有效期之后, 而且产品厂商又提供不出更新后的认证证书, 针对此种情况如何判定产品合规性?**

> **A:** 分两种情况讨论：证书有效期和标准时效性。
>
> * **认证证书有效期内:** 如无特殊情况和安全风险, **密码产品的采购合同签订时间**如果在商用密码产品认证证书的有效期内, 则可判定为产品合规。
> * **产品依据的相关标准失效或更新:** 在测评过程中, 如密评人员发现密码产品的相关密码标准已经失效或更新, 有义务告知信息系统单位相关情况, **建议其选用依据最新标准的密码产品**。
>   ==End of OCR for page 9==

==Start of OCR for page 10==
**Q 4.2 (新增问题): 有些类型的密码产品部署模式为客户端与服务端共同作用, 实现密码技术的应用。在测评时应如何判断实际部署中涉及客户端的密码产品的合规性? 三步依次执行判定。**

> **A:** 按照以下三步进行判断：
>
> 1. **第一步：检查证书范围**
>    * 查看商密认证证书中是否包括客户端、服务端。可查看证书确认，或要求厂商提供检测报告。
> 2. **第二步：核对产品一致性**
>    * **服务端/客户端一致性核查:** 确认被测系统中部署的密码产品与送检产品密码边界的一致性。按照证书或检测报告核查服务端和客户端一致性, 并且部署产品与送检产品的密码边界一致；产品版本一致或高于送检版本 (版本迭代原因仅为bug修订, 必要时厂商提供变更说明)。
>    * **实际密码需求判定合规性:** 如果不一致, 可考虑根据实际密码应用需求及密码使用情况等因素进行合规性判定。例如: SSL VPN与安全浏览器送检, 部署是改为普通浏览器。如果只实现服务端单向鉴别, 则重点查服务端 (检查浏览器SSL协议套件)。
> 3. **第三步：核对功能一致性**
>    * 结合认证证书检测报告, 对比密码应用方案中产品功能描述和实际使用功能。

> **PS:** 如果客户端和服务端是两个独立的、经过检测认证合格的密码产品, 则直接判定产品合规。

**Q 4.3 (新增问题): 如何确定密码产品的密码功能边界, 如签名验签服务器实现加解密功能, 在测评时如何判定其合规性?**

> **A:**
>
> * 签名验签服务器在产品检测时, 依据GM/T0029《签名验签服务器技术规范》和GM/T0028《密码模块安全技术要求》进行检测, 除了检测签名验签服务器的签名验签功能外, **也检测各算法的加解密功能、性能**。
> * 认证产品的安全等级符合要求且实际测评时确认了和送检产品密码功能的一致性, 则使用签名验签服务器进行加解密密码应用时**可以把签名验签服务器视为合规的密码产品**。
>   ==End of OCR for page 10==

==Start of OCR for page 11==
**Q 4.4: 经认证合格的密码产品, 《信息系统密码应用测评要求》中5.2密钥管理安全性测评是否可以直接判定为“符合”?**

> **A: 不能直接判定为“合格”。**
>
> 经过认证合格的密码产品仅仅是密钥管理安全性判定为“符合”的**必要条件**。
>
> **还需要满足以下几个方面：**
>
> * **满足39786相应等级的要求:** 例如: 三级信息系统采用满足二级及以上安全要求的密码产品。
> * **密码产品和密钥管理制度:** 按照产品配套的安全策略文档正确部署和使用, 密钥管理制度能保证该产品被正确地部署和使用。
> * **与密码应用方案一致:** 系统的实现方式与方案设计一致。
> * **密码方案和密钥体系:** 密钥管理机制与方案一致/密钥体系设计合理、实现正确。
> * **外部密钥保护:** 密钥在密码产品外部进行管理时, 进行相应保护 (如存储/备份/归档时的机密性完整性保护)。
>   ==End of OCR for page 11==

==Start of OCR for page 12==

### 问题5: 通过代码实现数据机密性、完整性保护的判定方法

**Q (修订问题): 未使用密码产品, 通过开源或自行开发的代码以软件实现密码算法的方式进行数据机密性、完整性保护, 结果怎么判定? 是否存在高风险?**

> **A:** 分两种情况讨论：
>
> * **情况一：自研软件实现且无法提供任何安全性证明**
>
>   * **判定:** **不符合**。
>   * **原因:** 无法判断其密码模块安全等级, 而且其采用的密码算法或技术实现安全性以及软实现带来的密钥管理安全性均无法保证。
> * **情况二：正确使用第三方开源密码库实现**
>
>   * **条件:** 第三方开源密码库是**经过长期使用且没有暴露出安全问题**的。
>   * **判定:** 可以考虑根据实际核查结果酌情判定为“**部分符合**”, 并给予相应分数。
>
> **参考标准:**
>
> * GM/T0115《信息系统密码应用测评要求》
> * GB/T39786-2021《信息安全技术信息系统密码应用基本要求》
>   ==End of OCR for page 12==

==Start of OCR for page 13==

### 问题6: 组合密码算法的量化评估和风险判定

**Q (新增问题): 【背景】组合使用密码算法实现数据安全保护时, 针对该情形的量化评估和风险判定。**

> **问题:** 针有些数据的传输或存储保护, 存在组合使用密码技术实现的情形, 如使用DES算法对数据变换后, 再使用SM4算法对DES加密后的密文进行变换, 即**SM4(DES(data))**, 应如何进行量化评估和风险判定?
>
> **解答:**
>
> * 由于加密算法的安全强度和算法复杂度、参数规模(密钥长度、分组长度等)等相关, 当加密算法组合使用时, **只要某一层加密算法安全强度足够, 且组合算法不使用同一密钥, 那么被保护数据就是安全的** (其他弱算法的运算可类似看成“未对明文做安全保护”)。
> * 因此, 当加密算法组合使用时, **量化评估和风险判定结果均依据安全强度较高的算法而定**。
> * 另外, 由于算法安全强度和密钥管理、算法实现正确性有关, 因此在进行相应测评对象的风险判定时还需综合**DAK判定情况**进行分析评价。
>   ==End of OCR for page 13==

==Start of OCR for page 14==

### 问题7: 如何开展分期规划、改造的信息系统的密码应用安全性评估

**Q (新增问题): 【背景】在进行信息系统密评时发现, 被测信息系统有通过评估的密码应用方案, 且方案中明确了该系统是分期做密码改造。例如, 由于经费原因, 应用和数据安全层面的身份鉴别指标不做改造但给出了合理的风险缓解措施, 且明确在二期建设中进行改造并给出改造的密码应用解决方案。在被测系统按照密码应用方案一期建设要求完成建设后, 开展密评。**

> **问题:** 针对上述情况, 在信息系统按密码应用方案完成一期建设后开展密评时, 该如何确定测评范围?
>
> **解答:**
>
> * 对于密码应用方案中涉及分期密码改造的情况, 在实际测评时, 应根据系统定级范围和密码应用需求**选取所有适用的测评指标和测评对象**开展系统密评并给出评估结论。
> * 即**测评指标和测评对象的选取范围与该系统是否进行分期密码改造无关**。
>   ==End of ocr for page 14==

==Start of OCR for page 15==

### 问题8: 已有通过评估的密码应用方案, 在实际密评时的注意事项

**Q (新增问题): 【背景】按照密评管理办法及相关管理要求, 网络与信息系统运营者在系统规划阶段完成密码应用方案评估, 在建设和运行阶段完成系统密评。**

> **问题:** 针对密码应用方案已经通过评估的信息系统, 在密评时发现实际情况不一致, 如何处理?
>
> **解答:**
>
> * 密码应用方案评估是在信息系统规划阶段, 对所编制的密码应用方案(或密码改造方案)进行评估。建成或已运行的信息系统的密码应用情况是否与规划阶段的一致, 需要在系统密评阶段(包括初次密评和定期密评)进行核实、验证。
> * 因此在实际开展系统密评工作时, 可参考通过评估的密码应用方案等文件了解系统业务和密码应用情况。
> * 如果在测评时发现实际建设情况与密码应用方案不一致, 则**需要系统运营者提供密码应用方案修订或优化的证明材料**, 但同时应**按实际测评时掌握的信息为准**, 并以实际情况得出测评结果。
>   ==End of OCR for page 15==


==Start of OCR for page 16==

## 02 | 密码应用技术类

==End of OCR for page 16==

==Start of OCR for page 17==

### 物理和环境安全

==End of OCR for page 17==

==Start of OCR for page 18==

#### 问题9 (修订问题): 物理和环境安全层面的测评对象识别和确定

**Q: 如何识别和确定物理和环境安全层面的测评对象？**

> **A:** 测评对象首先是被测信息系统所涉及的**所有物理机房**，具体实现上则关注机房的**电子门禁系统和视频监控系统**。
>
> **针对部署在外部机房（如IDC、运营商机房、云服务商机房）的情况：**
>
> * 物理和环境安全层面**仍然适用**。
> * **判定逻辑：**
>   * **如果** 外部机房**已经历过密评**，且评估结果为“**符合**”或“**基本符合**”（且云平台等级 ≥ 云上系统等级），则可以**复用相关测评结论**。
>   * **如果** 外部机房**未经历过密评**或评估结果为“**不符合**”，则需要：
>     1. 进行**现场取证测评**。
>     2. 要求**机房运维方提供相关证明**。
>        ==End of OCR for page 18==

==Start of OCR for page 19==

#### 问题9.2 (新增问题): 机房采用多层门禁时如何选择测评检查点？

**Q: 当被测信息系统的机房采用多层门禁措施, 且不同门禁采取的物理访问身份鉴别机制不一致时 (例如外层门禁采用非密码技术, 而内层门禁采用经检测认证合格的安全门禁系统实现), 则物理和环境安全层面的“身份鉴别”指标该如何选择测评检查点?**

> **A:** 根据服务器的部署情况分场景讨论。
>
> * **场景一: 多重保护 (图9-1)**
>
>   * 服务器受内外两层门禁的共同保护。
>   * **测评对象:** **任选其一**。
>   * **特别地:** 如果门禁1与门禁2之间物理区域还有非授权人员可以访问的话, 则**必须对门禁2测评**; 而外层门禁可能会起到安全加固和安全风险缓解作用, 密评机构需根据实际情况进行确认。
> * **场景二: 分布在多层门禁中 (图9-2)**
>
>   * 部分服务器在门禁1内，部分在门禁2内。
>   * **测评对象:** **选门禁1** (外层门禁)。
> * **场景三: 分布在多个门禁中 (图9-3)**
>
>   * 服务器分布在两个并行的门禁区域内。
>   * **测评对象:** **门禁1和2都选**。
>     ==End of OCR for page 19==

==Start of OCR for page 20==

#### 问题10 (修订问题): 电子门禁记录数据存储完整性测评时的注意事项

> **背景:** 一些信息系统所在机房已经部署合规的电子门禁系统, 厂商也声称机房进出记录受到完整性保护。
>
> **问题:** 对于符合GM/T0036-2014《采用非接触卡的门禁系统密码应用技术指南》标准的电子门禁系统, 其门禁系统内置的进出记录数据存储的完整性保护是否可以直接采信产品检测结果, 如不可直接采信, 是否需要门禁系统厂商提供其他证据?
>
> **解答:**
>
> * 由于GM/T0036中规定的相关要求, 主要针对的是采用基于非接触式卡的门禁系统对机房进出人员**身份鉴别**这一场景。
> * 因此, 对门禁系统进行密评实施时, 按照密评相关标准, 需要相关密码厂商提供门禁系统**进出记录数据存储完整性保护的相关证据**, 并提供实现该密码应用的**密码产品检测认证合规性的证明**。
>   ==End of OCR for page 20==

==Start of OCR for page 21==

### 网络和通信安全

==End of OCR for page 21==

==Start of OCR for page 22==

#### 问题11: 网络和通信安全层面的测评对象识别与确定

**Q: 如何确定网络层面的测评对象?**

> **A:** 网络和通信安全层面的测评对象主要是针对**跨网络访问的通信信道**, 这里的跨网络访问指的是从**不受保护的网络区域**访问被测系统。可以从**通信主体**和**网络类型**两个方面来确定：
>
> 1. **网络类型:** 主要依据网络之间是否相对独立进行分类, 如互联网、政务外网、企业专网等。
> 2. **通信主体:** 指的是参与通信的各方, 典型的如客户端与服务端。例如, PC机上运行的浏览器与服务器上运行的web服务系统, 移动智能终端上运行的APP与服务器上运行的应用系统; 也可以是服务端与服务端, 例如, IPSecVPN与IPSecVPN之间。

| 序号                       | 网络类型           | 测评对象                                     | 测评内容    |
| :------------------------- | :----------------- | :------------------------------------------- | :---------- |
| 1                          | 互联网             | 非国密浏览器与对外应用系统间的通信信道(业务) | HTTPS协议   |
| 2                          | 互联网             | 国密浏览器与对外应用系统间的通信信道(业务)   | HTTPS协议   |
| 3                          | 互联网             | VPN客户端与运维SSL VPN间通信信道(运维)       | SSL VPN协议 |
| 4                          | 政务外网与办公内网 | 政务外网VPN客户端与内网SSL VPN间通信信道     | SSL VPN协议 |
| 5                          | 办公内网           | 国密浏览器与后台应用系统间的通信信道         | HTTPS协议   |
| 6                          | 政务外网           | 非国密浏览器与对外应用系统间的通信信道(业务) | HTTPS协议   |
| 7                          | 政务外网           | 国密浏览器与对外应用系统间的通信信道(业务)   | HTTPS协议   |
| 8                          | (多个网络)         | IPSec VPN之间通信信道                        | IPSec协议   |
| ==End of OCR for page 22== |                    |                                              |             |

==Start of OCR for page 23==

#### 问题11 (续): 网络通信层面测评对象相关问题

**Q11.2: 被测系统与第三方电子认证服务相关系统之间的通信信道是否需要纳入测评范围?**

> **A:** 如果该通信信道经过了**不可控的网络环境**(比如互联网等), 该条通信信道应该**纳入**“网络和通信安全”层面的测评对象进行测评。

**Q11.3: 若被测系统为独立的内网系统, 且不允许跨网络边界进行远程运维, 是否可认为网络层没有测评对象?**

> **A:** 这类情况下还需要确定**网络边界环境**及**网络内部的安全性**, 再行决定。

**Q11.4 (新增/替换): 针对双活机房之间通信的通信链路, 是否可作为网络和通信层面的一条通信信道?**

> **A:** 因重要业务数据、重要个人信息等数据会在双活机房之间进行传输, 双活机房之间的通信链路无论是通过**运营商专线**还是采用**物理裸光纤**进行数据通信, 均应将该通信信道作为测评对象并参照GM/T0115《信息系统密码应用测评要求》进行测评。对于裸光纤的情况, 若不作为测评对象, 则应在密码应用方案中详细说明并评估。

**Q11.5: 跨网络边界的系统之间的交互, 如果通信主体不在其责任范围之内, 此通信信道是否需要纳入测评范围?**

> **A:** 跨网络边界的系统之间的交互所搭建的通信信道, 无论通信主体是否属于被测系统, 该通信信道都应**纳入**“网络和通信安全”层面的测评对象进行测评。
> ==End of OCR for page 23==

==Start of OCR for page 24==

#### 问题12 (新增问题): 网络和通信安全层面的身份鉴别

> **背景:** SSLVPN安全网关除了支持符合国家或密码行业标准要求的安全传输服务以外, 往往也支持基于国外密码算法的安全传输服务。很多应用系统往往会使用通用浏览器和SSLVPN安全网关相搭配的安全传输服务。
>
> **问题:** 采用合规的SSLVPN安全网关, 但安全通道使用安全强度足够高的国外密码算法套件, 且客户端采用通用浏览器, 此时网络和通信安全层面的“身份鉴别”指标该如何进行量化评估?
>
> **解答:**
>
> * **单向身份鉴别:** 由于考虑到客户端不需要对私钥进行密钥管理(无私钥参与密码运算的需求), 因此量化评估中D、A、K最高可为 **D(√)、A(×)、K(√)**。
> * **双向身份鉴别:** 由于在客户端需要进行私钥运算, 如果采用的是通用浏览器, 密钥管理上存在安全问题, 因此D、A、K最高可为 **D(√)、A(×)、K(x)**。
>   ==End of OCR for page 24==

==Start of OCR for page 25==

#### 问题13: 网络层安全接入认证和身份鉴别指标的差别

> **背景:** GB/T39786-2021《信息安全技术信息系统密码应用基本要求》在8.2节中要求e)可采用密码技术对从外部连接到内部网络的设备进行接入认证, 确保接入的设备身份真实性。
>
> **问题:** 如何理解“可采用密码技术对从外部连接到内部网络的设备进行接入认证, 确保接入的设备身份真实性”? 安全接入认证和身份鉴别如何进行区分?
>
> **解答:**
>
> * **身份鉴别:** 适用于两个实体通过**不可控的网络**(比如互联网)进行通信之前进行的身份鉴别, 如IPSecVPN或者SSL客户端/服务器的场景, IPSecVPN之间或者SSL客户端和服务端之间的鉴别。
> * **安全接入认证:** 适用于设备**“物理地”从外部接入**信息系统的内部网络之前对设备的身份鉴别, 接入后, 该设备将成为信息系统内部网络的一部分。如智能手持移动终端设备接入信息系统网络的场景, 对于移动智能终端设备接入的认证。
>   ==End of OCR for page 25==


==Start of OCR for page 26==

### 设备和计算安全

==End of OCR for page 26==

==Start of OCR for page 27==

#### 问题14: 设备和计算安全层面的测评对象识别与确定

> **背景:** GM/T0115《信息系统密码应用测评要求》在设备和计算层面的测评对象包括: 通用设备(及其操作系统、数据库管理系统)、网络及安全设备、密码设备、各类虚拟设备以及提供相应密码功能的密码产品。
>
> **问题:** 如何确定设备和计算安全层面的测评对象?
>
> **解答:**
>
> * **主要包括:**
>   * **通用服务器** (如应用服务器、数据库服务器)
>   * **数据库管理系统**
>   * **整机类和系统类的密码产品**
>   * **堡垒机** (当系统使用堡垒机用于对设备进行集中管理时, 不涉及应用和数据安全层面)
> * **一般不作为测评对象:**
>   * **交换机、网闸、防火墙、WAF**等未使用密码功能的网络设备、安全设备。
>
> **建议:** 在密评报告中, 对设备和计算层面的测评对象进行分类整理和描述。至少分为密码产品/设备、具有密码功能的网络及安全设备、采用密码技术的其他产品、通用设备、不具有密码功能的网络及安全设备、虚拟设备和系统。
> ==End of OCR for page 27==

==Start of OCR for page 28==

#### 问题15: 设备和计算安全层面测评对象选取粒度

> **背景:** 在一些较大型信息系统中, 针对上述每一类测评对象, 普遍均会部署**多台设备** (如部署多台服务器或者部署服务器集群, 部署多台IPSecVPN以与不同的外界通信实体建立通信信道)。在这种情况下, 在编写《商用密码应用安全性评估报告》时, 设备和计算安全层面各个测评对象确定结果所选取的粒度会影响报告最后得分。
>
> **问题:** 如何确定设备和计算安全层面的各个测评对象选取的粒度?
>
> **解答:**
>
> * **通用服务器和堡垒机:** 以“**具有相同硬件、软件配置的设备**”为粒度确定测评对象, 即具有相同硬件配置(如生产厂商、型号等)和软件配置(如操作系统版本、中间件等)的服务器/堡垒机作为一个测评对象。
> * **针对整机类密码产品、系统类密码产品:** 以“**具有相同商用密码产品认证证书编号的密码产品**”为粒度确定测评对象, 即具有同一商用密码产品认证证书的密码产品作为一个测评对象。
>
> **评分原则:**
>
>> 各测评对象所包含的各个设备(密码设备)的实际使用情况的**最低分值赋分**。
>> ==End of OCR for page 28==
>>

==Start of OCR for page 29==

#### 问题16: 设备和计算安全层面的身份鉴别

> **背景:** 某些信息系统只能在本地进行设备登录运维, 但是设备部署在相对安全的机房内部。由于设备改造难度较大, 难以对设备的登录机制进行整改。
>
> **问题 16.1:** 仅进行本地运维的设备, 如何针对设备和计算安全层面的“身份鉴别”和“远程管理通道安全”该如何进行测评和结果判定。
>
> **解答:**
>
> * 测评机构需要首先核实设备确实**仅进行本地运行, 关闭了对外运维的接口**。
> * 核实后, 该测评对象的“**远程管理通道安全**”测评指标可作为**不适用项**，“**身份鉴别**”测评指标为**适用项**。
> * “身份鉴别”测评结果为不符合时, 如果信息系统采用了必要的**缓解手段** (如《信息系统密码应用高风险判定指引》文件中所描述的采用基于特定设备或生物识别技术保证用户身份的真实性), 又或是将仅支持本地管理的被运维设备**单独安置在具有良好安防措施的密闭区域**(如机柜)内且**仅有设备运维人员才有该区域的访问权限**, 可**酌情降低风险等级**。
>   ==End of OCR for page 29==

==Start of OCR for page 30==

#### 问题16.2 (修订问题): 如果信息系统通过堡垒机统一运维管理设备, 堡垒机及其被运维设备的身份鉴别指标如何进行判定?

> **A:** 需要区分合规性判定和风险判定。
>
> **判定流程图:**
>
> 1. **合规性判定:**
>    * 对堡垒机和被管设备的身份鉴别指标进行**分别判定**。
> 2. **风险判定:**
>    * **场景一: 直接通过堡垒机运维 (本地登录)**
>      * 如果堡垒机自身的身份鉴别测评结论为“**符合**”或“**部分符合**”且**无高风险**，则可以**缓解**被管设备的身份鉴别风险。
>    * **场景二: 通过VPN运维，同时满足以下条件**
>      * 堡垒机仅对VPN地址开启远程管理端口，并限制堡垒机本地登录。
>      * VPN是**运维专用**的安全网关，运维人员也无法绕过网关远程运维。
>      * VPN客户端与安全网关之间的通信信道采用密码技术进行**通信实体双向身份鉴别** (例如，采用UKEY、动态口令、MAC、数字签名等机制且机制正确有效)。
>      * 满足以上条件，则可以**缓解**被管设备的身份鉴别风险。
>        ==End of OCR for page 30==

==Start of OCR for page 31==

#### 问题17: 远程管理通道安全的测评要点

**Q: 设备和计算安全层面“远程管理通道安全”测评项如何避免与网络和通信安全层面的测评对象重复测评, 如何进行量化评估?**

> **A:** 两个层面的测评对象不同。
>
> * **网络和通信安全层面:** 测评**跨越网络边界**的远程管理通道。
> * **设备和计算安全层面:** 仅测评与测评对象**直接相连**的信息传输通道。
>
> **场景一: 从互联网访问VPN, 在内网通过HTTPS登录堡垒机, 通过SSH跳转到被运维设备**
>
> * **网络层通道测评范围:** 从**运维端**到**SSLVPN**网关。
> * **设备层通道测评范围:**
>   * 对于堡垒机，是**SSLVPN**到**堡垒机**的HTTPS通道。
>   * 对于服务器，是**堡垒机**到**服务器**的SSH通道。
>
> **场景二: 从互联网通过HTTPS登录堡垒机, 通过SSH跳转到被运维设备**
>
> * **网络层通道测评范围:** 从**运维端**到**堡垒机**的HTTPS通道。
> * **设备层通道测评范围:**
>   * 对于服务器，是**堡垒机**到**服务器**的SSH通道。
>     ==End of OCR for page 31==

==Start of OCR for page 32==

#### 问题18: 合规密码产品的设备层身份鉴别、完整性相关指标的判定

> **前提:**
>
> 1. 具有《商用密码产品认证证书》 + 实际部署的密码产品与获认证产品一致
> 2. (同效) 具有《密码检测证书》
>
> **判定逻辑:**
>
> * **身份鉴别:**
>   * **不能直接判定符合。**
>   * **整机类密码设备:** 需要查看是否采用了密码技术(如智能IC卡、智能密码钥匙、动态口令等)，并是否按照产品使用要求进行身份鉴别。如果配套的终端密码产品没有独立的认证证书或不包含在整机检测报告中，则需进一步判定其密钥管理安全性，可能为**不符合**。
>   * **系统类密码设备:** 其软件部分所在服务器和所含整机类密码产品应分别作为测评对象，并单独实施测评。
> * **完整性指标 (系统资源访问控制、日志记录、重要可执行程序):**
>   * **二级及以上密码设备:** 判定为**符合**。
>   * **一级密码设备:** 需确认是否采用了带有密钥的核准密码技术实现软件/固件的完整性校验。如果确认**未采用**，则判定为**部分符合**。
>     ==End of OCR for page 32==

==Start of OCR for page 33==

### 应用和数据安全

==End of OCR for page 33==

==Start of OCR for page 34==

#### 问题19: 应用和数据安全层面的测评对象识别与确定

**Q19.1: 如何确定应用层面的测评对象?**

> **A:** 应用层面的测评对象由**关键应用**和**关键数据**共同确定。
>
> * **识别来源:**
>
>   * 通过评估的密码应用方案
>   * 等保定级报告描述的范围
> * **关键应用:**
>
>   * 被测系统的**所有业务应用**
> * **关键数据:**
>
>   * 鉴别数据
>   * 重要业务数据
>   * 重要审计数据
>   * 个人敏感信息
>   * 其他重要数据类型

**Q19.2: 在云应用/平台测评中, 密码资源池的密码管理平台应作为应用层的测评对象, 还是设备层的测评对象?**

> **A:** 在云应用/平台测评中, 密码资源池的密码管理平台应作为**应用和数据安全层面**的一个**管理类应用系统**进行测评。
> ==End of OCR for page 34==

==Start of OCR for page 35==

#### 问题19 (续): 应用和数据安全层面测评对象识别与确定

**Q19.3 (新增问题): 通过前端设备采集数据, 并且在前端会有存储设备或功能模块收集相关数据, 是否需要纳入测评范围?**

> **A:** 应结合信息系统所在**行业关于数据分级的标准和要求**, 以及信息系统的**网络安全等级保护测评报告**和**密码应用方案**(或密码改造方案)进行相关数据重要程度的确定。将所确定的重要数据纳入测评范围, 并根据数据实际安全需求开展测评。
> **[行业标准] + [等保报告] + [密评方案] -> 确定数据重要性 -> 纳入测评范围**

**Q19.4 (新增问题): 信息系统使用了第三方提供的数据异地备份的服务, 备份数据是否纳入测评范围?**

> **A:** 备份数据作为生产数据的副本, 其**密码应用需求与生产数据一致**。还应注意核查数据备份时**跨网络传输的安全性**。
> ==End of OCR for page 35==

==Start of OCR for page 36==

#### 问题20 (新增问题): 应用和数据安全层面的身份鉴别

> **背景:** 大量应用系统的身份鉴别使用第三方提供的身份认证服务(此处特指第三方身份认证服务为已单独等保定级的系统)。例如, 采用政务集约化建设模式, 应用系统用户由第三方(包括统一身份认证平台等)提供的身份认证服务进行身份鉴别。此外, 某些应用系统在与其他系统对接时也存在身份鉴别的需求。
>
> **问题:** 对于此类提供身份认证服务的第三方系统是否要求通过密评后再提供身份认证服务? 若该第三方系统未通过密评, 是否可以直接认为其应用和数据安全层面的“身份鉴别”指标为不符合?
>
> **解答:**
>
> * **已通过密评(“符合”或“基本符合”)的第三方身份认证服务系统:** 确认第三方身份认证服务的有效性后, **可复用**该指标相关结论。**鉴别机制的过程中重要数据传输安全进行延伸测评**。
> * **未通过密评或未做密评的第三方身份认证服务系统:** 如果第三方身份认证系统上**保存有应用系统的身份鉴别信息**, 应对第三方身份认证服务系统上相关的密码应用进行**(重新)测评**, 例如, 重要数据存储的机密性和完整性。
>
> **注意:**
>
>> 跨网络区域调用的情况还需要考虑被测系统与第三方身份认证服务系统之间的“**网络和通信安全层面**”的测评。
>> ==End of OCR for page 36==
>>

==Start of OCR for page 37==

#### 问题21: 重要数据机密性、完整性保护的实现方法问题

**Q21.1 (修订问题): 对于数据库中的重要数据存储完整性保护, 仅使用SM3进行保护能否判定为符合?**

> **A:** 仅使用SM3算法实现数据存储完整性功能, 在安全机制方面存在缺陷。入侵者可在篡改数据后, 重新进行SM3杂凑运算并覆盖原先的杂凑值。因此, 应判定为**不符合**。
> **建议:** 建议使用合规的密码产品通过 **HMAC-SM3**、**基于SM4的MAC** (参考GB/T15852.1-2020)或**数字签名**的方式实现数据存储的完整性。

**Q21.2 (新增问题): 使用经检测认证合格的服务器密码机(符合相应等级的密码模块安全要求)中的SM4-ECB算法(密钥管理由服务器密码机完成)对重要数据进行机密性保护, 能否判定为符合?**

> **A:** 分组密码的ECB模式, 因其特殊的工作流程, 无法隐蔽数据模式(如使用同一个密钥会导致相同的明文分组产生相同的密文分组), 也不能抵抗对分组的重放、嵌入、删除等攻击。因此, **不推荐**在密码应用中使用ECB模式, 并且在测评时应**告知对方安全风险**; 若有明显安全风险, 应进一步敦促其改正以免影响风险判定结果。但在测评判定时, **按照目前测评的现行准则, 可判定为符合**。
> ==End of OCR for page 37==


==Start of OCR for page 38==

#### 问题23: 访问控制信息的具体含义

**Q: GB/T39786中多次提到“访问控制信息”，其具体含义是什么？**

> **A: 访问控制是在身份鉴别的基础上, 控制主体对客体整体资源信息的访问控制管理。** 其实现机制包括但不限于：目录表、访问控制列表、能力表、访问控制矩阵、访问控制安全标签列表和权限位。
>
> **不同层面的“访问控制信息”：**
>
> * **网络和通信安全层面 (保证网络边界访问控制信息的完整性)**
>
>   * 指部署在网络边界的设备中的访问控制策略，例如：
>     * VPN中的访问控制列表
>     * 防火墙的访问控制列表
>     * 边界路由的访问控制列表
> * **设备和计算安全层面 (保证系统资源访问控制信息的完整性)**
>
>   * 指对设备系统资源的访问控制策略，例如：
>     * 设备操作系统的系统权限访问控制信息
>     * 系统文件目录的访问控制信息
>     * 数据库中的数据访问控制信息
>     * 堡垒机等第三方运维系统中的权限访问控制信息
> * **应用和数据安全层面 (保证信息系统应用访问控制信息的完整性)**
>
>   * 指应用系统自身的访问控制策略，例如：
>     * 应用系统的权限、标签等能够决定系统应用访问控制的措施等信息
>       ==End of OCR for page 38==

==Start of OCR for page 39==

#### 问题24 (新增问题): 跨网络调用密码资源实现相应密码功能的测评要点

> **背景:** 信息系统跨网络边界调用密码资源实现其密码应用安全需求时, 通常存在两种场景: 一是调用主体与密码资源部署在同一机房内, 但在不同的网络区域; 二是密码资源与调用主体部署于不同机房, 如某地建立统一密码服务平台为该区域内的所有信息系统提供密码应用支撑。
>
> **问题:** 针对于跨网络边界调用密码资源的情况, 在测评时应重点关注哪些问题?
>
> **解答:**
>
> * **测评指标:** 被测信息系统跨网络边界调用密码资源(如服务器密码机、统一密码服务平台等)时, 该**调用通道**应作为**网络和通信安全层面**的测评对象进行“**身份鉴别**”、“**通信数据完整性**”、“**通信过程中重要数据的机密性**”、“**网络边界访问控制信息的完整性**”等指标的测评。
> * **量化评估:** 针对**密钥管理安全(K)**, 除关注密码产品/密码服务的合规性、密钥生命周期管理机制的安全性外, 还应关注具体密码资源调用时的**鉴别与访问控制机制**, 即核实密码资源(如密码服务平台)和具体密码功能是否被授权的合法实体访问和调用。
>   ==End of OCR for page 39==

==Start of OCR for page 40==

## 03 | 密码应用管理类

==End of OCR for page 40==

==Start of OCR for page 41==

#### 问题25: 缺少密码应用方案的合规性判定

> **背景:** GB/T39786—2021 9.7节a)条款规定“应依据密码相关标准和密码应用需求, 制定密码应用方案”, 但很多已建信息系统并没有在系统规划时制定密码应用方案。
>
> **问题:** 在依据GB/T39786—2021 9.7a)条款密评时, 密码应用方案该如何理解?
>
> **解答:**
>
> * **已建信息系统:** 其密码应用方案**不追溯**到系统最初规划时的方案, 该信息系统根据相关标准、密码应用需求以及前期密评的整改建议, 制定的**密码应用改造方案可视为该系统的密码应用方案**。后续, 即可依据GM/T0115《信息系统密码应用测评要求》的相关要求进行判定。
> * **新建信息系统:** 除依据GM/T0115进行符合性判定外, 还应按照《信息系统密码应用高风险判定指引》进行风险评价。
>   ==End of OCR for page 41==

==Start of OCR for page 42==

#### 问题26: 投入运行前未进行密码应用安全性评估的合规性判定

> **背景:** GB/T39786-2021 8.7节d)条款规定“投入运行前应进行密码应用安全性评估, 评估通过后系统方可正式运行”, 但很多已建信息系统并没有在投入运行前进行密码应用安全性评估。
>
> **问题:** 被测信息系统为已建运行系统, 其建设完成时未进行投入运行前的商用密码应用安全性评估; 或者, 被测信息系统经密码应用改造后, 在重新上线前进行了商用密码应用安全性评估。建设运行层面“投入运行前进行密码应用安全性评估”测评项应如何判定?
>
> **解答:**
>
> * **2020/1/1之前投入运行的信息系统:** 密评时该项可判定为“**不适用**”。
> * **2020/1/1之后投入运行的信息系统:** 则密评时该项判定为“**不符合**”。
> * **注:** 若被测信息系统后期依据通过评估的密码应用方案进行了密码应用改造, 在重新投入运行前进行了密码应用安全性评估且评估结论为“符合”或“基本符合”, 此时**投入运行时间可按照密码应用改造后重新上线的时间进行确定**, 该项可判定为“**符合**”。
> * 对于**分期密码建设**的第三/四级信息系统, 投入运行时间原则上按照第一期建设完成后的上线时间进行确定, 但可酌情后延。
>   ==End of OCR for page 42==

==Start of OCR for page 43==

#### 问题27 (新增问题): 如何开展信息系统密码应用成熟度极低情况下的密码应用管理测评

> **背景:** 在对一些密码应用成熟度极差的存量信息系统密评时发现, 被测系统除涉及口令通过计算杂凑值(代码实现)进行传输/存储保护或设备远程管理使用SSH协议外, 未部署任何密码产品或未使用其他任何密码技术对被测系统业务进行安全防护, 因此被测单位未按照GB/T39786-2021中密码应用管理要求落实。
>
> **问题:** 针对部分存量信息系统自身密码应用成熟度极低, 未部署任何密码产品对其业务进行密码安全防护, 而导致密码应用管理方面无法配套建设的情形, 在测评时该如何把握?
>
> **解答:**
>
> * 在密评时仍需将**密码应用管理纳入测评内容**, 测评过程中需要进一步考察这四个层面相关制度文件与实际结合情况。
> * 如果存在相关制度文件满足指标要求情况, 但实际信息系统并未落实密码应用岗位的人员、密码软硬件部署和相应密钥管理等密码应用相关内容时, 对应测评单元结果判定时最高为“**部分符合**”。
>   ==End of OCR for page 43==

==Start of OCR for page 44==

## 04 | 量化评估类

==End of OCR for page 44==

==Start of OCR for page 45==

#### 问题28 (新增问题): 《量化评估规则(2023版)》中, 密码使用有效性D项的判定

**Q: 使用了合规产品中存在安全问题或安全强度不足的密码算法(如DES、MD5等), 密码使用有效性D应如何判定和量化?**

> **A: “密码使用有效性D”可判定为符合, 但不同安全强度的密码算法量化评估分值不同。**
>
> **判定逻辑:**
>
> 1. **判定有效性(D):** 当密码技术为提供了机密性、完整性、真实性或不可否认性的保护时被**正确、有效使用**, 则“密码使用有效性D”可判定为**符合**。
> 2. **量化评估(A):** D项符合后，再根据算法的**安全强度**在“密码算法/技术合规性A”维度进行量化评分。
>
> **安全强度分档:**
>
> * **安全强度 ≥ 112比特**
>   * 如3DES(3密钥)、AES-128及以上、SHA-224及以上、RSA-2048及以上、ECDSA_P224及以上
> * **80比特 ≤ 安全强度 < 112比特**
>   * 如RSA-1024、3DES(2密钥)、ECDSA-P192
> * **安全强度 < 80比特**
>   * 如DES、MD5、SHA-1、RSA-512
>     ==End of OCR for page 45==

==Start of OCR for page 46==

#### 延伸知识: 《量化评估规则(2023版)》

*(Image: Cover of the document "商用密码应用安全性评估量化评估规则 (2023版)")*

* **发布日期:** 2023年7月17日
* **实施日期:** 2023年8月1日
* **发布单位:** 中国密码学会密评联委会

**核心框架:**

* **01 密码使用有效性 (Cryptography Deployment effectiveness)**
* **02 密码算法/技术合规性 (Cryptography Algorithm/Technique compliance)**
* **03 密钥管理安全 (Key management security)**

**评估流程:**
测评对象得分 -> 测评单元得分 -> 安全层面得分 -> 整体得分
==End of OCR for page 46==

==Start of OCR for page 47==

#### 延伸知识: 《量化评估规则(2023版)》量化评估表

*(Image: A detailed table showing the quantization rules)*

**核心思想:** **鼓励使用更高强度的密码算法/技术和通过认证的更高等级的密码模块!**

*(注：此页为P10的详细版本，展示了完整的量化评估表、Ra修正参数表和Rk修正参数表，内容与P10转录一致，此处不再重复。)*
==End of OCR for page 47==

==Start of OCR for page 48==

## 05 | 风险判定类

==End of OCR for page 48==

==Start of OCR for page 49==

#### 问题29: 有缓解措施的高风险判定

> **背景:** 《信息系统密码应用高风险判定指引》文件中, 部分判定单元中提供了可能的缓解措施。
>
> **问题:** 如果被测系统使用了缓解措施, 那么被测系统的风险等级、测评结论、分数是否发生改变?
>
> **解答:**
>
> * 基于当前密码技术和产业发展现状,《指引》中提供了可能的缓解措施，旨在调动系统责任单位开展安全整改工作的积极性。
> * 如果被测系统使用了相应的缓解措施, 并通过评估**确认缓解措施有效**, 原则上可以**酌情降低其风险等级**。
> * 但还应结合被测系统的实际情况进行分析, 确认缓解措施能够有效抵御相关威胁, 否则仍然维持其风险等级不变。
> * 如果因缓解措施导致风险等级发生改变, 将可能造成**测评结论发生改变**, 但**测评分数仍保持不变**。
>   ==End of OCR for page 49==

==Start of OCR for page 50==

#### 问题30 (修订问题): 报告中对于高风险缓解措施的体现

> **背景:** 在实际测评过程中, 经常会发现系统存在《指引》中描述的高风险安全问题, 但是同时又采取了一定的缓解措施, 那么如何在测评报告中体现高风险安全问题和缓解措施之间的关系呢?
>
> **问题:** 针对测评报告模板中高风险修正过程在哪个地方体现?
>
> **解答:**
>
> * 如果测评报告问题涉及高风险判例中的问题场景, 则需要在报告**附录A**中, 按照实际评估结果进行填写。
> * 针对该测评项判定为**不符合**, 但是在**风险分析**时针对该安全风险进行**缓解措施说明**。
>
> **示例表格 (风险分析):**
>
> | 序号                       | 安全层面       | 问题描述                                        | 关联威胁   | 风险分析                                                                                                                                      | 风险等级 (高/中/低) |
> | :------------------------- | :------------- | :---------------------------------------------- | :--------- | :-------------------------------------------------------------------------------------------------------------------------------------------- | :------------------ |
> | 1-1                        | 物理和环境安全 | XXXX 未使用密码技术对机房访问人员进行身份鉴别。 | 物理和环境 | 可能导致非法人员进入物理环境...; 但是现阶段机房使用了**指纹识别技术**对进入人员进行身份鉴别, 能够在一定程度上降低该安全问题带来的风险。 | 0 / 1 / 0           |
> | ==End of OCR for page 50== |                |                                                 |            |                                                                                                                                               |                     |

==Start of OCR for page 51==

#### 问题31 (修订问题): 应用层身份鉴别是否可以缓解网络层身份鉴别的高风险

> **背景:** “身份鉴别”指标是“网络和通信安全”“应用和数据安全”两个层面的关键性指标。但在实际测评过程中, 这两个“身份鉴别”指标可能存在模糊不清、混淆的情况。
>
> **问题:** “网络和通信安全”“应用和数据安全”的身份鉴别指标有何区别? 在测评时如何把握两者的关系?
>
> **解答:**
>
> * **测评对象不同:**
>   * **网络层**身份鉴别的具体测评对象是客户端与服务器建立的通信信道中的**通信实体的身份**。
>   * **应用层**身份鉴别的具体测评对象是关键业务应用的**用户**。
>   * 因此**无法**在报告模板的5.1小节进行测评结果修正(即**无法对测评结果和分值进行“弥补”**)。
> * **风险评估时综合考虑:**
>   * 在风险评估时, 仍应当结合其他层面的“身份鉴别”指标测评结果, 对本层面的“身份鉴别”指标进行综合考虑。
>   * 比如, “网络和通信安全”进行了对接入的用户设备进行了身份鉴别, 但“应用和数据安全”未额外使用身份鉴别机制, 而是直接使用了“网络和通信安全”的“身份鉴别”结果, 默认接入的用户可以登录应用。
>   * 此时, “应用和数据安全”身份鉴别指标判定仍为“**不符合**”, 但需要结合“网络和通信安全”层面的身份鉴别机制和“应用和数据安全”层面相关的工作机制, 给出合适的**风险等级**。
>     ==End of OCR for page 51==

==Start of OCR for page 52==

#### 问题32 (新增问题): 如何理解高风险判定指引中的“适用时”

> **背景:** 在《信息系统密码应用高风险判定指引(2021)》中, 网络和通信安全层面、应用和数据安全层面相关高风险项的安全问题中, 均涉及了“适用时”。
>
> **问题:** 在做风险分析和评价时, 对于《指引》文件中, 部分高风险项的安全问题所提到的“适用时”该如何理解?
>
> **解答:**
>
> * 《指引》文件中的“**适用时**”, 是指针对相应指标的密码应用, **目前密码产业供给侧存在经检测认证合规的密码产品**(即密码产品种类在《商用密码产品认证目录》中)且该密码产品能够满足应用场景实际需求, 则视为“**适用**”。
> * 需要注意的是, 若密评时判定为“**不适用**”, 则须在密评报告的**风险分析环节详细论述原因**。
>   ==End of OCR for page 52==

==Start of OCR for page 53==

#### 延伸知识: 《信息系统密码应用高风险判定指引》

*(Image: Cover of the document and a complex mind map showing the high-risk items)*

**高风险判定指引框架:**

* **通用要求 (适用所有级别)**
  * 密码算法, 密码技术, 密码产品和服务
* **技术要求**
  * **物理和环境:** 身份鉴别 (二级以上)
  * **网络和通信:** **身份鉴别**, **通信过程中重要数据的机密性**, 安全接入认证 (四级)
  * **设备和计算:** **身份鉴别**, **远程管理通道安全**
  * **应用和数据:** **身份鉴别**, **重要数据传输机密性**, **重要数据存储机密性**, **重要数据存储完整性**, **不可否认性**
* **管理要求 (适用二级以上)**
  * 具备密码应用安全管理制度, 制定密码应用方案

*注: 标红色的项为无缓解措施的高风险项。*
==End of OCR for page 53==

==Start of OCR for page 54==

## 06 | 特殊场景类

==End of OCR for page 54==

==Start of OCR for page 55==

#### 问题33: 云平台测评的责任和范围

> **背景:** 随着信息技术的发展, 云计算已经被广泛应用...实际测评中, 经常会碰到云平台和云租户业务应用系统密码应用(以下称为“云上应用”)的密评, 但是相对于传统的信息系统, 云平台和云上应用的运营者一般不同。
>
> **问题:** 对运行在云平台上的云上应用进行密评时, 特别是云平台和云上应用的运营者不同的情况下, 如何界定两者的责任和范围?
>
> **解答:**
>
> 1. “**云平台测评**”的责任主体为**云平台的运营者**。
> 2. “**云上应用测评**”的责任主体为**云上应用的运营者**。
>
> **原则上, 云上应用系统所处的云平台通过密评后, 云上应用系统才能通过密评。云上应用系统所处的云平台的安全级别应不低于云上应用系统。**
> ==End of OCR for page 55==

==Start of OCR for page 56==

#### 问题34.1 (修订问题): 云平台和云上应用的测评方式和测评结论复用方式

**Q: 对运行在云平台上的云上应用进行密评时, 那么如何分别对这两个系统进行分别测评, 测评结论能够复用吗?**

> **A:**
>
> * **01 云平台测评**
>
>   * 对于云平台自身而言, 除了从物理和环境、网络和通信、设备和计算、应用和数据四个技术层面的密码应用进行测评外, 还要兼顾云平台的服务模式, 即要分别对云平台支持的每类服务模式(**IaaS、PaaS、SaaS**)进行密码应用测评, 并关注对云租户提供的密码服务都有哪些, 每台密码设备服务的边界。因此, 在测评结论中还须包含“**云平台密码支撑能力说明**”。
> * **02 云上应用测评 (前提: 云平台通过密评且安全级别≥云上应用)**
>
>   * 云上应用密评时, 应重点关注应用本身在各个安全层面的密码应用情况。
>   * **被完全评估的支撑能力所支撑:** **复用**云平台相关测评对象的测评结论。
>   * **被部分评估的支撑能力所支撑:** 结合“云平台支撑能力说明”对测评对象进行**充分测评**并给定结果。
>   * **调用了非云平台提供的支撑能力:** 需要进行**单独测评**; 但是要重点关注该支撑能力与云平台、云上应用进行整合时是否安全, 避免可能存在的安全风险。

> **支撑能力定义:**
>
> * **被完全评估的支撑能力:** 指的是云平台中的某些测评对象, 这些测评对象同时用于支撑云平台和云上应用, 将同时作为云平台和云上应用的测评对象。
> * **被部分评估的支撑能力:** 指的是云平台提供的某些支撑服务, 这些支撑服务仅用于云上应用而不用于云平台, 或者将服务于云平台和云上应用的不同测评对象。
>   ==End of OCR for page 56==

==Start of OCR for page 57==

#### 问题34.2 (新增问题): 云平台和密码服务平台分离场景下的报告要求

> **问题:** 在《商用密码应用安全性评估报告模板(2023版)》的“被测信息系统基本信息表”中增加了“系统是否依赖不在本系统范围内的云平台运行”, 但对于云平台和密码服务平台(此处特指密码服务平台为已单独完成等保定级的系统)分别独立开展密评的情况, 租户的密评结论会同时依赖于云平台和密码服务平台...针对这种情况, 云平台和密码服务平台密评时, 需额外提供哪些信息作为云上应用密评时的参考资料。
>
> **解答:**
>
> * 对于该情况, 云平台和密码服务平台(特指已单独完成等保定级的系统)在各自密评报告中, 除给出系统密评结论外, 还需提供“**被完全评估的支撑能力说明**”“**被部分评估的支撑能力说明**”。
> * 例如, 云上应用业务运转在云平台上, 云上应用租用第三方云密码服务提供商的密码服务平台。那么, 云平台的密评报告需体现“被完全评估的支撑能力说明”, 密码服务平台的密评报告需体现“被部分评估的支撑能力说明”。
> * 另外, 在云上应用密评时, 可考虑在密评报告的附录中体现所涉及云平台和密码服务平台的密评结论、被完全评估的支撑能力说明、被部分评估的支撑能力说明。
>   ==End of OCR for page 57==

==Start of OCR for page 58==

#### 问题35: 面向公众等网站的测评

> **背景:** 政务信息公开网站、门户网站等面向公众的信息系统, 具有内容可以公开、任何人都可以访问的特点, 相应指标的测评需要进行额外的考量。
>
> **问题:** 面向公众、信息可公开的信息系统, 需要重点关注哪些内容?
>
> **解答:**
>
> * 首先, 需要确定哪些人员可以访问该信息系统。除了**面向公众用户**之外, 信息系统一般需要**管理员**对该系统进行管理, 管理员的**身份鉴别、传输通道安全**等显然需要与一般信息系统一样, 遵循相应的测评指标进行测评。
> * 其次, 对于**公众用户**而言, 仍需要对网站进行**身份鉴别**(比如防止钓鱼网站), 并对其内容的**完整性**进行保护;
> * 一般情况下还需要对用户访问网站产生的**隐私数据**(如访问情况、隐私行为等)进行保护, 因此仍然需要测评公众用户相关的网络和通信安全层面“**身份鉴别**”“**通信数据完整性**”“**通信过程中重要数据的机密性**”等指标。
>   ==End of OCR for page 58==

==Start of OCR for page 59==

#### 问题36 (新增问题): 特殊网络系统的测评对象选取

> **背景:** 现阶段, 大量的纯网络的信息系统被单独定级, 如基础网络系统、骨干网络系统、电子政务外网等网络型信息系统, 极端情况下骨干网仅涉及核心路由器、交换机、防火墙等网络基础设备。由于这类系统的核心保护内容通常并非是应用系统和数据, 因此其测评与一般的信息系统会有差异。
>
> **问题:** 针对这种纯网络系统, 如何开展密评工作? 网络和通信安全、设备和计算安全、应用和数据安全层面的测评对象如何选取?
>
> **解答:**
>
> * **应用和数据安全层面:** 网络管理等相关的应用。
> * **设备和计算安全层面:** 重要应用所在应用服务器、数据库及数据库服务器、密码产品、堡垒机等。
> * **网络和通信安全层面:** 根据信息系统网络连接情况, 从通信主体和网络类型这两个方面进行确定。
> * 如果单独定级的网络系统仅涉及核心路由器、交换机、防火墙等网络基础设备且不含有任何应用, 那么建议密评机构进一步参考密码应用(或改造)方案、网络安全等级保护测评报告, 以确认相关系统开展密评的可行性。
>   ==End of OCR for page 59==

==Start of OCR for page 60==

* **Logo 1:** OLYMPIC COUNCIL OF ASIA (亚奥理事会)
* **Logo 2:** (Stylized S-like logo, 安恒信息)
* **Text:** 亚奥理事会官方合作伙伴 (OFFICIAL PRESTIGE PARTNER OF THE OLYMPIC COUNCIL OF ASIA)

## 联系我们 (Contact Us)

浙江省杭州市滨江区西兴街道联慧街188号安恒大厦
www.dbappsecurity.com.cn
400-6059-110

*(Image: QR Code for "安恒信息官方微信")*
安恒信息官方微信
==End of OCR for page 60==
